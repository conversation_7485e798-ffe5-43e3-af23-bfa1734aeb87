#!/bin/bash

# 自动化部署脚本（支持多模式运行）
# 使用方法示例:
#   ./deploy_and_start.sh -s                # 仅停止服务
#   ./deploy_and_start.sh -r                # 重启服务（不打包）
#   ./deploy_and_start.sh -u                # 上传现有jar

# 参数解析
MODE="full"
OPTS=""

while getopts "sruh" opt; do
  case $opt in
    s) MODE="stop" ;;
    r) MODE="restart" ;;
    u) MODE="upload" ;;
    h) echo "使用帮助..."; exit 0 ;;
    *) exit 1 ;;
  esac
done

# 显式设置工作目录为项目根目录
SCRIPT_DIR="$(cd "$(dirname "$0")/../../" && pwd)"
PROJECT_DIR="$SCRIPT_DIR"  # 定义项目根目录变量

cd "$PROJECT_DIR" || { echo "无法切换到项目根目录: $PROJECT_DIR"; exit 1; }

REMOTE_USER="root"
REMOTE_HOST="**************"
SSH_PORT=2233  # 固定端口配置（根据需要修改此值）
REMOTE_DIR="/data/project/coocaa"

# 打印所有变量
echo "当前模式: $MODE"
echo "项目根目录: $PROJECT_DIR"
echo "远程用户: $REMOTE_USER"
echo "远程主机: $REMOTE_HOST"
echo "SSH端口: $SSH_PORT"
echo "远程目录: $REMOTE_DIR"
echo "JAR路径: $JAR_PATH"


# 验证端口有效性
echo "$SSH_PORT" | grep -E "^[1-9][0-9]{0,4}$" > /dev/null
if [ $? -ne 0 ]; then
    echo "无效的SSH端口: $SSH_PORT"
    exit 1
fi

# 核心功能函数
package_app() {
    echo "执行Maven打包..."
    cd "$PROJECT_DIR" && mvn clean package -DskipTests || { echo "Maven打包失败"; exit 1; }
}

stop_service() {
    echo "停止远程服务..."
    ssh -p $SSH_PORT "${REMOTE_USER}@${REMOTE_HOST}" "cd ${REMOTE_DIR} && ./start.sh stop" || { echo "停止服务失败"; exit 1; }
}

backup_jar() {
    echo "备份远程服务器上的旧版本..."
    ssh -p $SSH_PORT "${REMOTE_USER}@${REMOTE_HOST}" << 'EOF'
mkdir -p ${REMOTE_DIR}/backup
if [ -f ${REMOTE_DIR}/yudao-server.jar ]; then
    mv ${REMOTE_DIR}/yudao-server.jar ${REMOTE_DIR}/backup/yudao-server.jar.$(date +%Y%m%d%H%M%S)
fi
EOF
    [ $? -eq 0 ] || { echo "远程备份操作失败"; exit 1; }
}

upload_jar() {
    echo "上传jar包到 ${REMOTE_USER}@${REMOTE_HOST}:${SSH_PORT}..."
    scp -P $SSH_PORT "$JAR_PATH" "${REMOTE_USER}@${REMOTE_HOST}:${REMOTE_DIR}/" || { echo "文件上传失败"; exit 1; }
}

start_service() {
    echo "在远程服务器执行启动脚本..."
    ssh -p $SSH_PORT "${REMOTE_USER}@${REMOTE_HOST}" "cd ${REMOTE_DIR} && ./start.sh start" || { echo "启动服务失败"; exit 1; }
}

# 主流程控制
# 验证端口有效性后
if [ $? -ne 0 ]; then
    echo "无效的SSH端口: $SSH_PORT"
    exit 1
fi

# 模式选择执行
if [ "$MODE" = "stop" ]; then
    stop_service
elif [ "$MODE" = "restart" ]; then
    stop_service && start_service
else
    # 默认流程（full/upload）
    if [ "$MODE" != "upload" ]; then
        package_app || { echo "Maven打包失败"; exit 1; }
    fi
    
    [ -f "$JAR_PATH" ] || { echo "找不到jar包: $JAR_PATH"; exit 1; }
    
    if [ "$MODE" = "upload" ]; then
        backup_jar && upload_jar && start_service
    else
        stop_service
        backup_jar
        upload_jar
        start_service
    fi
fi



echo "部署和启动完成"