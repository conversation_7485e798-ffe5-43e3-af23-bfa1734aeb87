package cn.iocoder.yudao.module.hrm.enums;

import cn.iocoder.yudao.framework.common.exception.ErrorCode;
/**
 * HRM 错误码枚举类
 *
 * HRM 系统，使用 9-002-000-000 段
 */
public interface ErrorCodeConstants {
    ErrorCode ASMT_QUESTION_NOT_EXISTS = new ErrorCode(9-002-000-100, "测评题不存在");
    ErrorCode ASMT_QUESTION_OPTION_NOT_EXISTS = new ErrorCode(9-002-000-200, "测评题选项不存在");
    ErrorCode ASMT_PAPER_NOT_EXISTS = new ErrorCode(9-002-000-300, "测评卷不存在");
    ErrorCode ASMT_PAPER_QUESTION_NOT_EXISTS = new ErrorCode(9-002-000-400, "测评卷与测评题关联不存在");
    ErrorCode ASMT_PACKAGE_NOT_EXISTS = new ErrorCode(9-002-000-500, "测评包不存在");
    ErrorCode ASMT_TASK_NOT_EXISTS = new ErrorCode(9-002-000-600, "测评任务不存在");
    ErrorCode ASMT_TASK_USER_NOT_EXISTS = new ErrorCode(9-002-000-610, "用户评测任务不存在");
    ErrorCode ASMT_TASK_USER_NOT_START = new ErrorCode(9-002-000-611, "评测未开始");
    ErrorCode ASMT_TASK_USER_END = new ErrorCode(9-002-000-612, "评测已结束");
    ErrorCode ASMT_ANSWERSHEET_NOT_EXISTS = new ErrorCode(9-002-000-700, "用户答题卡不存在");
    ErrorCode ASMT_ANSWERSHEET_EXISTS = new ErrorCode(9-002-000-701, "您已提交测评，请勿重复提交");
    ErrorCode ASMT_MODEL_NOT_EXISTS = new ErrorCode(9-002-000-800, "测评模型不存在");
    ErrorCode ASMT_MODEL_NOT_MATCH = new ErrorCode(9-002-000-801, "没有对应的测评模型");
    ErrorCode ASMT_INDEX_NOT_EXISTS = new ErrorCode(9-002-000-900, "测评指标不存在");
    ErrorCode ASMT_INDEX_IMPORT_ENPTY = new ErrorCode(9-002-000-901, "导入指标数据为空");
    ErrorCode ASMT_INDEX_NAME_ENPTY = new ErrorCode(9-002-000-902, "指标名称不能为空");
    ErrorCode ASMT_PACKAGE_PAPER_NOT_EXISTS = new ErrorCode(9-002-000-1000, "测评包-测评卷关联不存在");
    ErrorCode ASMT_INDEX_RESULT_NOT_EXISTS = new ErrorCode(9-002-000-1100, "测评指标计算结果不存在");

    ErrorCode ASMT_INDEX_STDSCORE_NOT_EXISTS = new ErrorCode(9-002-000-1200, "指标原始分转标准分区间设置不存在");
    ErrorCode ASMT_INDEX_BENCHMARK_NOT_EXISTS = new ErrorCode(9-002-000-1300, "序列岗位指标基准不存在");
}
