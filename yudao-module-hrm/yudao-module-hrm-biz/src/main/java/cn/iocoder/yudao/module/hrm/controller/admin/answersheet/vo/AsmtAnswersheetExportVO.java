package cn.iocoder.yudao.module.hrm.controller.admin.answersheet.vo;

import com.alibaba.excel.annotation.ExcelIgnoreUnannotated;
import com.alibaba.excel.annotation.ExcelProperty;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

@Schema(description = "管理后台 - 用户答题卡 Response VO")
@Data
@ExcelIgnoreUnannotated
public class AsmtAnswersheetExportVO {

    @Schema(description = "序号", example = "1")
    @ExcelProperty("序号")
    private Integer quesSort;

    @Schema(description = "题目")
    @ExcelProperty("题目")
    private String quesTitle;

    @Schema(description = "答案")
    @ExcelProperty("答案")
    private String answer;

    @Schema(description = "得分")
    @ExcelProperty("得分")
    private Double score;
}
