package cn.iocoder.yudao.module.hrm.controller.admin.task.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotNull;
import lombok.Data;

@Schema(description = "管理后台 - 测评任务-任务用户关联新增/修改 Request VO")
@Data
public class AsmtTaskUserSaveReqVO {

    @Schema(description = "标签id", requiredMode = Schema.RequiredMode.REQUIRED, example = "18826")
    private Long id;

    @Schema(description = "测评任务id", example = "5952")
    private Long taskId;

    @Schema(description = "用户id", requiredMode = Schema.RequiredMode.REQUIRED, example = "6424")
    @NotNull(message = "用户id不能为空")
    private Long userId;

    @Schema(description = "状态")
    private Integer status;
}
