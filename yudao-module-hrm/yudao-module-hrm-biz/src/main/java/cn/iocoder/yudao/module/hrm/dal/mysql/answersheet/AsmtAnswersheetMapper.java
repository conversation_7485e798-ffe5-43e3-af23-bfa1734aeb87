package cn.iocoder.yudao.module.hrm.dal.mysql.answersheet;

import cn.iocoder.yudao.framework.common.pojo.PageResult;
import cn.iocoder.yudao.framework.mybatis.core.mapper.BaseMapperX;
import cn.iocoder.yudao.framework.mybatis.core.query.LambdaQueryWrapperX;
import cn.iocoder.yudao.module.hrm.controller.admin.answersheet.vo.AsmtAnswersheetPageReqVO;
import cn.iocoder.yudao.module.hrm.controller.admin.answersheet.vo.AsmtAnswersheetRespVO;
import cn.iocoder.yudao.module.hrm.controller.admin.answersheet.vo.AsmtAnswersheetSearchReqVO;
import cn.iocoder.yudao.module.hrm.controller.admin.answersheet.vo.AsmtAnswersheetSearchRespVO;
import cn.iocoder.yudao.module.hrm.dal.dataobject.answersheet.AsmtAnswersheetDO;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;

import java.util.List;

/**
 * 用户答题卡 Mapper
 *
 * <AUTHOR>
 */
@Mapper
public interface AsmtAnswersheetMapper extends BaseMapperX<AsmtAnswersheetDO> {

    default PageResult<AsmtAnswersheetDO> selectPage(AsmtAnswersheetPageReqVO reqVO) {
        return selectPage(reqVO, new LambdaQueryWrapperX<AsmtAnswersheetDO>()
                .eqIfPresent(AsmtAnswersheetDO::getUserId, reqVO.getUserId())
                .eqIfPresent(AsmtAnswersheetDO::getTaskId, reqVO.getTaskId())
                .eqIfPresent(AsmtAnswersheetDO::getPkgId, reqVO.getPkgId())
                .eqIfPresent(AsmtAnswersheetDO::getPaperId, reqVO.getPaperId())
                .eqIfPresent(AsmtAnswersheetDO::getQuesId, reqVO.getQuesId())
                .orderByDesc(AsmtAnswersheetDO::getId));
    }

    List<AsmtAnswersheetRespVO> selectListByUserTaskId(@Param("userTaskId") Long userTaskId, @Param("paperId") Long paperId);

    List<AsmtAnswersheetSearchRespVO> searchPage(@Param("reqVO") AsmtAnswersheetSearchReqVO reqVO);
    Long searchCount(@Param("reqVO") AsmtAnswersheetSearchReqVO reqVO);

    /**
     * 根据测评任务ID汇总完成答题用户数
     * @param taskId 测评任务ID（非任务与用户关联表ID）
     * @param userId 用户ID，非空则查指定用户是否已完成答题，如果结果为1则已完成，否则未完成
     * @return 完成答题人数
     */
    Long getUserCountByTaskId(@Param("taskId") Long taskId, @Param("userId") Long userId);

    /**
     * 获取完成答题任务的用户ID列表
     * @param taskId 任务
     * @return
     */
    @Select("SELECT user_id FROM hrm_asmt_answersheet WHERE task_id=#{taskId} AND deleted=0 GROUP BY user_id HAVING COUNT(DISTINCT paper_id)=(SELECT COUNT(DISTINCT b.id) FROM hrm_asmt_task a INNER JOIN hrm_asmt_package_paper b ON a.pkg_id=b.pkg_id WHERE a.id=#{taskId} AND b.deleted=0)")
    List<Long> getAllUserIdsByTaskId(@Param("taskId") Long taskId);

    default List<AsmtAnswersheetDO> selectListByTaskUser(Long taskId, Long userId){
        return selectList(new LambdaQueryWrapperX<AsmtAnswersheetDO>()
                .eq(AsmtAnswersheetDO::getTaskId, taskId)
                .eq(AsmtAnswersheetDO::getUserId, userId)
                .orderByAsc(AsmtAnswersheetDO::getPaperId)
                .orderByAsc(AsmtAnswersheetDO::getId));
    }
}
