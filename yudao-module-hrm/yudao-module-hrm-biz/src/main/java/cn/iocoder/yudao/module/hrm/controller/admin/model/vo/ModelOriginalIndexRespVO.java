package cn.iocoder.yudao.module.hrm.controller.admin.model.vo;

import cn.iocoder.yudao.module.hrm.controller.admin.paper.vo.AsmtPaperQuestionRespVO;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Builder;
import lombok.Data;

import java.util.List;

@Schema(description = "管理后台 - 模型关联的原始指标（试卷及其题目) Response VO")
@Data
@Builder
public class ModelOriginalIndexRespVO {

    @Schema(description = "测评卷id")
    private Long paperId;

    @Schema(description = "测评卷名称")
    private String paperName;

    @Schema(description = "试卷类型")
    private String paperType;

    @Schema(description = "显示顺序")
    private Integer sort;

    @Schema(description = "题目列表")
    private List<AsmtPaperQuestionRespVO> questionList;
}


