package cn.iocoder.yudao.module.hrm.utils;

import org.apache.commons.lang3.StringUtils;

import java.util.HashMap;
import java.util.HashSet;
import java.util.Map;
import java.util.Set;
import java.util.regex.Matcher;
import java.util.regex.Pattern;

/**
 * 数学公式计算工具类
 * 支持解析数学公式，检查变量是否都已提供值，并计算结果
 *
 * <AUTHOR>
 */
public class FormulaCalculator {

    /**
     * 变量占位符的正则表达式
     */
    private static final Pattern VARIABLE_PATTERN = Pattern.compile("\\$\\{([^}]+)}");

    /**
     * 运算符正则表达式
     */
    private static final Pattern OPERATOR_PATTERN = Pattern.compile("[+\\-*/]");

    /**
     * 数字正则表达式
     */
    private static final Pattern NUMBER_PATTERN = Pattern.compile("-?\\d+(\\.\\d+)?");

    /**
     * 括号正则表达式
     */
    private static final Pattern BRACKET_PATTERN = Pattern.compile("[()]");

    /**
     * 计算数学公式
     *
     * @param formula 数学公式，例如：${a} + ${b} * ${c}
     * @param variables 变量值映射，例如：{"a": 1, "b": 2, "c": 3}
     * @return 计算结果
     * @throws IllegalArgumentException 如果公式中的变量没有全部提供值或公式格式错误
     */
    public static double calculate(String formula, Map<String, Double> variables) {
        if (StringUtils.isBlank(formula)) {
            return 0.0;
        }
        if (variables == null) {
            variables = new HashMap<>();
        }

        // 1. 检查公式格式
        validateFormulaFormat(formula);

        // 2. 提取公式中的所有变量
        Set<String> formulaVariables = extractVariables(formula);

        // 3. 检查是否所有变量都已提供值
        Set<String> missingVariables = new HashSet<>();
        for (String variable : formulaVariables) {
            if (!variables.containsKey(variable)) {
                missingVariables.add(variable);
            }
        }

        if (!missingVariables.isEmpty()) {
            throw new IllegalArgumentException("公式中缺少以下变量的值: " + String.join(", ", missingVariables));
        }

        // 4. 替换公式中的变量为实际值
        String processedFormula = replaceVariables(formula, variables);

        // 5. 计算结果
        return evaluateExpression(processedFormula);
    }

    /**
     * 检查公式格式是否正确
     *
     * @param formula 数学公式
     * @throws IllegalArgumentException 如果公式格式错误
     */
    private static void validateFormulaFormat(String formula) {
        // 检查括号匹配
        int openBrackets = 0;
        for (int i = 0; i < formula.length(); i++) {
            if (formula.charAt(i) == '(') {
                openBrackets++;
            } else if (formula.charAt(i) == ')') {
                openBrackets--;
                if (openBrackets < 0) {
                    throw new IllegalArgumentException("公式中的括号不匹配，在位置 " + i + " 处有多余的右括号");
                }
            }
        }
        if (openBrackets > 0) {
            throw new IllegalArgumentException("公式中的括号不匹配，缺少 " + openBrackets + " 个右括号");
        }

        // 检查变量格式
        Matcher matcher = VARIABLE_PATTERN.matcher(formula);
        while (matcher.find()) {
            String variable = matcher.group(1);
            if (StringUtils.isBlank(variable)) {
                throw new IllegalArgumentException("公式中存在空的变量表达式，如 ${}");
            }
            if (!variable.matches("[a-zA-Z0-9_]+")) {
                throw new IllegalArgumentException("变量名 '" + variable + "' 包含非法字符，只允许字母、数字和下划线");
            }
        }

        // 检查运算符
        // 检查连续运算符
        if (formula.matches(".*[+\\-*/]{2,}.*")) {
            throw new IllegalArgumentException("公式中存在连续的运算符");
        }

        // 检查开头和结尾的运算符
        if (formula.matches("^[+\\-*/].*") || formula.matches(".*[+\\-*/]$")) {
            throw new IllegalArgumentException("公式不能以运算符开头或结尾");
        }

        // 检查除数为零的情况
        if (formula.contains("/0") || formula.contains("/ 0")) {
            throw new IllegalArgumentException("公式中可能存在除数为零的情况");
        }

        // 检查变量替换后的表达式是否合法
        String testFormula = formula.replaceAll("\\$\\{[^}]+\\}", "1");
        try {
            // 尝试计算一个简单的替换版本，看是否能解析
            evaluateExpression(testFormula);
        } catch (Exception e) {
            throw new IllegalArgumentException("公式格式错误: " + e.getMessage());
        }
    }

    /**
     * 提取公式中的所有变量
     *
     * @param formula 数学公式
     * @return 变量集合
     */
    private static Set<String> extractVariables(String formula) {
        Set<String> variables = new HashSet<>();
        Matcher matcher = VARIABLE_PATTERN.matcher(formula);
        while (matcher.find()) {
            variables.add(matcher.group(1));
        }
        return variables;
    }

    /**
     * 替换公式中的变量为实际值
     *
     * @param formula 数学公式
     * @param variables 变量值映射
     * @return 替换后的公式
     */
    private static String replaceVariables(String formula, Map<String, Double> variables) {
        String result = formula;
        for (Map.Entry<String, Double> entry : variables.entrySet()) {
            result = result.replace("${" + entry.getKey() + "}", String.valueOf(entry.getValue()));
        }
        return result;
    }

    /**
     * 计算表达式结果
     *
     * @param expression 表达式
     * @return 计算结果
     * @throws IllegalArgumentException 如果表达式格式错误
     */
    private static double evaluateExpression(String expression) {
        // 使用简单的表达式求值方法
        // 注意：这是一个简化版本，只支持基本的四则运算
        // 对于更复杂的表达式，建议使用第三方库如 exp4j 或 JEP

        // 移除所有空格
        expression = expression.replaceAll("\\s+", "");

        // 检查表达式是否为空
        if (StringUtils.isBlank(expression)) {
            throw new IllegalArgumentException("表达式不能为空");
        }

        // 处理括号
        while (expression.contains("(")) {
            int start = expression.lastIndexOf("(");
            int end = expression.indexOf(")", start);
            if (end == -1) {
                throw new IllegalArgumentException("表达式中的括号不匹配");
            }
            String subExpression = expression.substring(start + 1, end);
            double subResult = evaluateExpression(subExpression);
            expression = expression.substring(0, start) + subResult + expression.substring(end + 1);
        }

        // 处理乘除
        while (expression.contains("*") || expression.contains("/")) {
            int index = -1;
            char operator = ' ';

            // 找到第一个乘除运算符
            for (int i = 0; i < expression.length(); i++) {
                if (expression.charAt(i) == '*' || expression.charAt(i) == '/') {
                    index = i;
                    operator = expression.charAt(i);
                    break;
                }
            }

            if (index == -1) {
                break;
            }

            // 提取左右操作数
            int leftStart = index - 1;
            while (leftStart >= 0 && (Character.isDigit(expression.charAt(leftStart)) || expression.charAt(leftStart) == '.' || expression.charAt(leftStart) == '-')) {
                leftStart--;
            }
            leftStart++;

            int rightEnd = index + 1;
            while (rightEnd < expression.length() && (Character.isDigit(expression.charAt(rightEnd)) || expression.charAt(rightEnd) == '.' || expression.charAt(rightEnd) == '-')) {
                rightEnd++;
            }

            try {
                double left = Double.parseDouble(expression.substring(leftStart, index));
                double right = Double.parseDouble(expression.substring(index + 1, rightEnd));

                double result;
                if (operator == '*') {
                    result = left * right;
                } else {
                    if (right == 0) {
                        throw new ArithmeticException("除数不能为零");
                    }
                    result = left / right;
                }

                expression = expression.substring(0, leftStart) + result + expression.substring(rightEnd);
            } catch (NumberFormatException e) {
                throw new IllegalArgumentException("表达式中的数字格式错误: " + e.getMessage());
            }
        }

        // 处理加减
        while (expression.contains("+") || expression.contains("-")) {
            int index = -1;
            char operator = ' ';

            // 找到第一个加减运算符（跳过负号）
            for (int i = 0; i < expression.length(); i++) {
                if ((expression.charAt(i) == '+' || expression.charAt(i) == '-') && (i == 0 || expression.charAt(i - 1) != 'E' && expression.charAt(i - 1) != 'e')) {
                    index = i;
                    operator = expression.charAt(i);
                    break;
                }
            }

            if (index == -1) {
                break;
            }

            // 提取左右操作数
            int leftStart = index - 1;
            while (leftStart >= 0 && (Character.isDigit(expression.charAt(leftStart)) || expression.charAt(leftStart) == '.' || expression.charAt(leftStart) == '-')) {
                leftStart--;
            }
            leftStart++;

            int rightEnd = index + 1;
            while (rightEnd < expression.length() && (Character.isDigit(expression.charAt(rightEnd)) || expression.charAt(rightEnd) == '.' || expression.charAt(rightEnd) == '-')) {
                rightEnd++;
            }

            try {
                double left = Double.parseDouble(expression.substring(leftStart, index));
                double right = Double.parseDouble(expression.substring(index + 1, rightEnd));

                double result;
                if (operator == '+') {
                    result = left + right;
                } else {
                    result = left - right;
                }

                expression = expression.substring(0, leftStart) + result + expression.substring(rightEnd);
            } catch (NumberFormatException e) {
                throw new IllegalArgumentException("表达式中的数字格式错误: " + e.getMessage());
            }
        }

        try {
            return Double.parseDouble(expression);
        } catch (NumberFormatException e) {
            throw new IllegalArgumentException("表达式格式错误，无法解析为数字: " + expression);
        }
    }

    /**
     * 使用示例
     */
    public static void main(String[] args) {
        // 示例1：基本计算
        Map<String, Double> variables1 = new HashMap<>();
        variables1.put("a", 10.0);
        variables1.put("b", 5.0);
        variables1.put("c", 2.0);
        String formula1 = "${a} + ${b} * ${c}";
        System.out.println("公式: " + formula1);
        System.out.println("结果: " + calculate(formula1, variables1));

        // 示例2：缺少变量
        Map<String, Double> variables2 = new HashMap<>();
        variables2.put("a", 10.0);
        variables2.put("b", 5.0);
        String formula2 = "${a} + ${b} * ${c}";
        System.out.println("公式: " + formula2);
        try {
            System.out.println("结果: " + calculate(formula2, variables2));
        } catch (IllegalArgumentException e) {
            System.out.println("错误: " + e.getMessage());
        }

        // 示例3：复杂表达式
        Map<String, Double> variables3 = new HashMap<>();
        variables3.put("x", 2.0);
        variables3.put("y", 3.0);
        variables3.put("z", 4.0);
        String formula3 = "(${x} + ${y}) * ${z} / 2";
        System.out.println("公式: " + formula3);
        System.out.println("结果: " + calculate(formula3, variables3));

        // 示例4：括号不匹配
        String formula4 = "(${a} + ${b}) * ${c}";
        System.out.println("公式: " + formula4);
        try {
            System.out.println("结果: " + calculate(formula4, variables1));
        } catch (IllegalArgumentException e) {
            System.out.println("错误: " + e.getMessage());
        }

        // 示例5：连续运算符
        String formula5 = "${a} + 1 + ${b}";
        System.out.println("公式: " + formula5);
        try {
            System.out.println("结果: " + calculate(formula5, variables1));
        } catch (IllegalArgumentException e) {
            System.out.println("错误: " + e.getMessage());
        }

        // 示例6：除数为零
        String formula6 = "${a} / 0";
        System.out.println("公式: " + formula6);
        try {
            System.out.println("结果: " + calculate(formula6, variables1));
        } catch (IllegalArgumentException e) {
            System.out.println("错误: " + e.getMessage());
        }

        // 示例7：非法变量名
        String formula7 = "${a@b} + ${c}";
        System.out.println("公式: " + formula7);
        try {
            System.out.println("结果: " + calculate(formula7, variables1));
        } catch (IllegalArgumentException e) {
            System.out.println("错误: " + e.getMessage());
        }
    }
}