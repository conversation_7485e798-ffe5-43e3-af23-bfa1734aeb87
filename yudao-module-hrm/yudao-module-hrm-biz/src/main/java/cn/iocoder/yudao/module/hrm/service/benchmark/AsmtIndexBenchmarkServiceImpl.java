package cn.iocoder.yudao.module.hrm.service.benchmark;

import cn.hutool.core.util.StrUtil;
import cn.iocoder.yudao.framework.mybatis.core.query.LambdaQueryWrapperX;
import cn.iocoder.yudao.module.system.api.user.AdminUserApi;
import cn.iocoder.yudao.module.system.api.user.dto.AdminUserRespDTO;
import org.springframework.stereotype.Service;
import jakarta.annotation.Resource;
import org.springframework.validation.annotation.Validated;

import cn.iocoder.yudao.module.hrm.controller.admin.benchmark.vo.*;
import cn.iocoder.yudao.module.hrm.dal.dataobject.benchmark.AsmtIndexBenchmarkDO;
import cn.iocoder.yudao.framework.common.pojo.PageResult;
import cn.iocoder.yudao.framework.common.util.object.BeanUtils;

import cn.iocoder.yudao.module.hrm.dal.mysql.benchmark.AsmtIndexBenchmarkMapper;

import java.util.List;

import static cn.iocoder.yudao.framework.common.exception.util.ServiceExceptionUtil.exception;
import static cn.iocoder.yudao.module.hrm.enums.ErrorCodeConstants.*;

/**
 * 序列岗位指标基准 Service 实现类
 *
 * <AUTHOR>
 */
@Service
@Validated
public class AsmtIndexBenchmarkServiceImpl implements AsmtIndexBenchmarkService {

    @Resource
    private AsmtIndexBenchmarkMapper asmtIndexBenchmarkMapper;

    @Resource
    private AdminUserApi adminUserApi;

    @Override
    public Long createAsmtIndexBenchmark(AsmtIndexBenchmarkSaveReqVO createReqVO) {
        // 插入
        AsmtIndexBenchmarkDO asmtIndexBenchmark = BeanUtils.toBean(createReqVO, AsmtIndexBenchmarkDO.class);
        asmtIndexBenchmarkMapper.insert(asmtIndexBenchmark);
        // 返回
        return asmtIndexBenchmark.getId();
    }

    @Override
    public void updateAsmtIndexBenchmark(AsmtIndexBenchmarkSaveReqVO updateReqVO) {
        // 校验存在
        validateAsmtIndexBenchmarkExists(updateReqVO.getId());
        // 更新
        AsmtIndexBenchmarkDO updateObj = BeanUtils.toBean(updateReqVO, AsmtIndexBenchmarkDO.class);
        asmtIndexBenchmarkMapper.updateById(updateObj);
    }

    @Override
    public void deleteAsmtIndexBenchmark(Long id) {
        // 校验存在
        validateAsmtIndexBenchmarkExists(id);
        // 删除
        asmtIndexBenchmarkMapper.deleteById(id);
    }

    private void validateAsmtIndexBenchmarkExists(Long id) {
        if (asmtIndexBenchmarkMapper.selectById(id) == null) {
            throw exception(ASMT_INDEX_BENCHMARK_NOT_EXISTS);
        }
    }

    @Override
    public AsmtIndexBenchmarkDO getAsmtIndexBenchmark(Long id) {
        return asmtIndexBenchmarkMapper.selectById(id);
    }

    @Override
    public AsmtIndexBenchmarkDO getIndexBenchmarkBySeriesLevelByUser(Long userId) {
        AdminUserRespDTO user = adminUserApi.getUser(userId);
        String series = user.getPositionSeries();
        String level = user.getJobLevel();
        if (StrUtil.isEmpty(series)) {
            series = "通用";
        }
        if (StrUtil.isEmpty(level)) {
            level = "通用";
        }

        return getIndexBenchmarkBySeriesLevel(series, level);
    }

    @Override
    public AsmtIndexBenchmarkDO getIndexBenchmarkBySeriesLevel(String series, String level) {
        List<AsmtIndexBenchmarkDO> list = asmtIndexBenchmarkMapper.selectList(
                new LambdaQueryWrapperX<AsmtIndexBenchmarkDO>()
                        .eqIfPresent(AsmtIndexBenchmarkDO::getSeries, series)
                        .eqIfPresent(AsmtIndexBenchmarkDO::getLevel, level).last(" limit 1 "));
        if (list.isEmpty()) {
            return null;
        }

        return list.get(0);
    }

    @Override
    public PageResult<AsmtIndexBenchmarkDO> getAsmtIndexBenchmarkPage(AsmtIndexBenchmarkPageReqVO pageReqVO) {
        return asmtIndexBenchmarkMapper.selectPage(pageReqVO);
    }

}
