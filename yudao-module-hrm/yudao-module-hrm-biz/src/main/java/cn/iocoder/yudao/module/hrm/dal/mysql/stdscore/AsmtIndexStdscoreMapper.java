package cn.iocoder.yudao.module.hrm.dal.mysql.stdscore;

import cn.iocoder.yudao.framework.common.pojo.PageResult;
import cn.iocoder.yudao.framework.mybatis.core.query.LambdaQueryWrapperX;
import cn.iocoder.yudao.framework.mybatis.core.mapper.BaseMapperX;
import cn.iocoder.yudao.module.hrm.dal.dataobject.stdscore.AsmtIndexStdscoreDO;
import org.apache.ibatis.annotations.Mapper;
import cn.iocoder.yudao.module.hrm.controller.admin.stdscore.vo.*;

/**
 * 指标原始分转标准分区间设置 Mapper
 *
 * <AUTHOR>
 */
@Mapper
public interface AsmtIndexStdscoreMapper extends BaseMapperX<AsmtIndexStdscoreDO> {

    default PageResult<AsmtIndexStdscoreDO> selectPage(AsmtIndexStdscorePageReqVO reqVO) {
        return selectPage(reqVO, new LambdaQueryWrapperX<AsmtIndexStdscoreDO>()
                .eqIfPresent(AsmtIndexStdscoreDO::getIndexCode, reqVO.getIndexCode())
                .orderByAsc(AsmtIndexStdscoreDO::getId));
    }

}