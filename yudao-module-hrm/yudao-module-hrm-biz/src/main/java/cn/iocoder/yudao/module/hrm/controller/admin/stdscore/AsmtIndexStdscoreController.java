package cn.iocoder.yudao.module.hrm.controller.admin.stdscore;

import org.springframework.web.bind.annotation.*;
import jakarta.annotation.Resource;
import org.springframework.validation.annotation.Validated;
import org.springframework.security.access.prepost.PreAuthorize;
import io.swagger.v3.oas.annotations.tags.Tag;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.Operation;

import jakarta.validation.*;
import jakarta.servlet.http.*;
import java.util.*;
import java.io.IOException;

import cn.iocoder.yudao.framework.common.pojo.PageParam;
import cn.iocoder.yudao.framework.common.pojo.PageResult;
import cn.iocoder.yudao.framework.common.pojo.CommonResult;
import cn.iocoder.yudao.framework.common.util.object.BeanUtils;
import static cn.iocoder.yudao.framework.common.pojo.CommonResult.success;

import cn.iocoder.yudao.framework.excel.core.util.ExcelUtils;

import cn.iocoder.yudao.framework.apilog.core.annotation.ApiAccessLog;
import static cn.iocoder.yudao.framework.apilog.core.enums.OperateTypeEnum.*;

import cn.iocoder.yudao.module.hrm.controller.admin.stdscore.vo.*;
import cn.iocoder.yudao.module.hrm.dal.dataobject.stdscore.AsmtIndexStdscoreDO;
import cn.iocoder.yudao.module.hrm.service.stdscore.AsmtIndexStdscoreService;

@Tag(name = "管理后台 - 指标原始分转标准分区间设置")
@RestController
@RequestMapping("/hrm/asmt-index-stdscore")
@Validated
public class AsmtIndexStdscoreController {

    @Resource
    private AsmtIndexStdscoreService asmtIndexStdscoreService;

    @PostMapping("/create")
    @Operation(summary = "创建指标原始分转标准分区间设置")
    @PreAuthorize("@ss.hasPermission('hrm:asmt-index-stdscore:create')")
    public CommonResult<Long> createAsmtIndexStdscore(@Valid @RequestBody AsmtIndexStdscoreSaveReqVO createReqVO) {
        return success(asmtIndexStdscoreService.createAsmtIndexStdscore(createReqVO));
    }

    @PutMapping("/update")
    @Operation(summary = "更新指标原始分转标准分区间设置")
    @PreAuthorize("@ss.hasPermission('hrm:asmt-index-stdscore:update')")
    public CommonResult<Boolean> updateAsmtIndexStdscore(@Valid @RequestBody AsmtIndexStdscoreSaveReqVO updateReqVO) {
        asmtIndexStdscoreService.updateAsmtIndexStdscore(updateReqVO);
        return success(true);
    }

    @DeleteMapping("/delete")
    @Operation(summary = "删除指标原始分转标准分区间设置")
    @Parameter(name = "id", description = "编号", required = true)
    @PreAuthorize("@ss.hasPermission('hrm:asmt-index-stdscore:delete')")
    public CommonResult<Boolean> deleteAsmtIndexStdscore(@RequestParam("id") Long id) {
        asmtIndexStdscoreService.deleteAsmtIndexStdscore(id);
        return success(true);
    }

    @GetMapping("/get")
    @Operation(summary = "获得指标原始分转标准分区间设置")
    @Parameter(name = "id", description = "编号", required = true, example = "1024")
    @PreAuthorize("@ss.hasPermission('hrm:asmt-index-stdscore:query')")
    public CommonResult<AsmtIndexStdscoreRespVO> getAsmtIndexStdscore(@RequestParam("id") Long id) {
        AsmtIndexStdscoreDO asmtIndexStdscore = asmtIndexStdscoreService.getAsmtIndexStdscore(id);
        return success(BeanUtils.toBean(asmtIndexStdscore, AsmtIndexStdscoreRespVO.class));
    }

    @GetMapping("/page")
    @Operation(summary = "获得指标原始分转标准分区间设置分页")
    @PreAuthorize("@ss.hasPermission('hrm:asmt-index-stdscore:query')")
    public CommonResult<PageResult<AsmtIndexStdscoreRespVO>> getAsmtIndexStdscorePage(@Valid AsmtIndexStdscorePageReqVO pageReqVO) {
        PageResult<AsmtIndexStdscoreDO> pageResult = asmtIndexStdscoreService.getAsmtIndexStdscorePage(pageReqVO);
        return success(BeanUtils.toBean(pageResult, AsmtIndexStdscoreRespVO.class));
    }

    @GetMapping("/export-excel")
    @Operation(summary = "导出指标原始分转标准分区间设置 Excel")
    @PreAuthorize("@ss.hasPermission('hrm:asmt-index-stdscore:export')")
    @ApiAccessLog(operateType = EXPORT)
    public void exportAsmtIndexStdscoreExcel(@Valid AsmtIndexStdscorePageReqVO pageReqVO,
              HttpServletResponse response) throws IOException {
        pageReqVO.setPageSize(PageParam.PAGE_SIZE_NONE);
        List<AsmtIndexStdscoreDO> list = asmtIndexStdscoreService.getAsmtIndexStdscorePage(pageReqVO).getList();
        // 导出 Excel
        ExcelUtils.write(response, "指标原始分转标准分区间设置.xls", "数据", AsmtIndexStdscoreRespVO.class,
                        BeanUtils.toBean(list, AsmtIndexStdscoreRespVO.class));
    }

}