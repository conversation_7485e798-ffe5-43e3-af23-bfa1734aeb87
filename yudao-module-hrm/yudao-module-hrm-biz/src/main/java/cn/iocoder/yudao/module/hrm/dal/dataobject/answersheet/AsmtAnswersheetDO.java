package cn.iocoder.yudao.module.hrm.dal.dataobject.answersheet;

import cn.iocoder.yudao.framework.mybatis.core.dataobject.BaseDO;
import com.baomidou.mybatisplus.annotation.KeySequence;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.*;

/**
 * 用户答题卡 DO
 *
 * <AUTHOR>
 */
@TableName("hrm_asmt_answersheet")
@KeySequence("hrm_asmt_answersheet_seq") // 用于 Oracle、PostgreSQL、Kingbase、DB2、H2 数据库的主键自增。如果是 MySQL 等数据库，可不写。
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class AsmtAnswersheetDO extends BaseDO {

    /**
     * 标签id
     */
    @TableId
    private Long id;
    /**
     * 用户id
     */
    private Long userId;
    /**
     * 测评任务id
     */
    private Long taskId;

    /** 测评任务与用户关联表的ID */
    private Long userTaskId;
    /**
     * 测试包id
     */
    private Long pkgId;

    /**
     * 测试卷id
     */
    private Long paperId;
    /**
     * 题目id
     */
    private Long quesId;
    /**
     * 题目顺序
     */
    private Integer quesSort;
    /**
     * 答案
     */
    private Long answerId;
    private String answer;
    private String answerCode;
    /**
     * 得分
     */
    private Double score;

}
