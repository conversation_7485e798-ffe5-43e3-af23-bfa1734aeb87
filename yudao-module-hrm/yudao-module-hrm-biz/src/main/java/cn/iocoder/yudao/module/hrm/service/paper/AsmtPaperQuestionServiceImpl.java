package cn.iocoder.yudao.module.hrm.service.paper;

import cn.iocoder.yudao.framework.common.pojo.PageResult;
import cn.iocoder.yudao.framework.common.util.object.BeanUtils;
import cn.iocoder.yudao.module.hrm.controller.admin.paper.vo.AsmtPaperQuestionPageReqVO;
import cn.iocoder.yudao.module.hrm.controller.admin.paper.vo.AsmtPaperQuestionRespVO;
import cn.iocoder.yudao.module.hrm.controller.admin.paper.vo.AsmtPaperQuestionSaveReqVO;
import cn.iocoder.yudao.module.hrm.controller.admin.paper.vo.AsmtPaperQuestionSortReqVO;
import cn.iocoder.yudao.module.hrm.controller.admin.question.vo.AsmtQuestionOptionRespVO;
import cn.iocoder.yudao.module.hrm.dal.dataobject.paper.AsmtPaperQuestionDO;
import cn.iocoder.yudao.module.hrm.dal.mysql.paper.AsmtPaperQuestionMapper;
import cn.iocoder.yudao.module.hrm.service.question.AsmtQuestionOptionService;
import com.baomidou.mybatisplus.core.conditions.update.UpdateWrapper;
import jakarta.annotation.Resource;
import jakarta.validation.Valid;
import org.springframework.stereotype.Service;
import org.springframework.validation.annotation.Validated;

import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

import static cn.iocoder.yudao.framework.common.exception.util.ServiceExceptionUtil.exception;
import static cn.iocoder.yudao.module.hrm.enums.ErrorCodeConstants.ASMT_PAPER_QUESTION_NOT_EXISTS;

/**
 * 测评卷与测评题关联 Service 实现类
 *
 * <AUTHOR>
 */
@Service
@Validated
public class AsmtPaperQuestionServiceImpl implements AsmtPaperQuestionService {

    @Resource
    private AsmtPaperQuestionMapper asmtPaperQuestionMapper;

    @Resource
    private AsmtQuestionOptionService questionOptionService;

    @Override
    public Long createAsmtPaperQuestion(AsmtPaperQuestionSaveReqVO createReqVO) {
        // 插入
        AsmtPaperQuestionDO asmtPaperQuestion = BeanUtils.toBean(createReqVO, AsmtPaperQuestionDO.class);
        asmtPaperQuestionMapper.insert(asmtPaperQuestion);
        // 返回
        return asmtPaperQuestion.getId();
    }

    @Override
    public void createAsmtPaperQuestionBatch(@Valid List<AsmtPaperQuestionSaveReqVO> list) {
        for (AsmtPaperQuestionSaveReqVO vo : list) {
            createAsmtPaperQuestion(vo);
        }
    }

    @Override
    public void updateAsmtPaperQuestion(AsmtPaperQuestionSaveReqVO updateReqVO) {
        // 校验存在
        validateAsmtPaperQuestionExists(updateReqVO.getId());
        // 更新
        AsmtPaperQuestionDO updateObj = BeanUtils.toBean(updateReqVO, AsmtPaperQuestionDO.class);
        asmtPaperQuestionMapper.updateById(updateObj);
    }

    @Override
    public void updateAsmtPaperQuestionSort(@Valid List<AsmtPaperQuestionSortReqVO> list) {
        for (AsmtPaperQuestionSortReqVO vo : list) {
            validateAsmtPaperQuestionExists(vo.getId());
            asmtPaperQuestionMapper.update(new UpdateWrapper<AsmtPaperQuestionDO>().eq("id", vo.getId()).set("sort", vo.getSort()));
        }
    }

    @Override
    public void deleteAsmtPaperQuestion(Long id) {
        // 校验存在
        validateAsmtPaperQuestionExists(id);
        // 删除
        asmtPaperQuestionMapper.deleteById(id);
    }

    private void validateAsmtPaperQuestionExists(Long id) {
        if (asmtPaperQuestionMapper.selectById(id) == null) {
            throw exception(ASMT_PAPER_QUESTION_NOT_EXISTS);
        }
    }

    @Override
    public AsmtPaperQuestionDO getAsmtPaperQuestion(Long id) {
        return asmtPaperQuestionMapper.selectById(id);
    }

    @Override
    public List<AsmtPaperQuestionRespVO> getAsmtPaperQuestionByPaperId(Long paperId) {
        return asmtPaperQuestionMapper.getByPaperId(paperId);
    }

    @Override
    public Object getPaperQuestionAndOptionsByPaperId(Long paperId) {
        // 获取问题列表
        List<AsmtPaperQuestionRespVO> questionRespVOList = getAsmtPaperQuestionByPaperId(paperId);
        // 获取问题选项
        List<AsmtQuestionOptionRespVO> optionDOList = questionOptionService.getQuestionOptionListByPaperId(paperId);
        Map<Long, List<AsmtQuestionOptionRespVO>> optionMap = optionDOList.stream().collect(Collectors.groupingBy(AsmtQuestionOptionRespVO::getQuseId));

        for (AsmtPaperQuestionRespVO question : questionRespVOList) {
            question.setOptionList(optionMap.get(question.getQuesId()));
        }
        return questionRespVOList;
    }

    @Override
    public PageResult<AsmtPaperQuestionDO> getAsmtPaperQuestionPage(AsmtPaperQuestionPageReqVO pageReqVO) {
        return asmtPaperQuestionMapper.selectPage(pageReqVO);
    }

}
