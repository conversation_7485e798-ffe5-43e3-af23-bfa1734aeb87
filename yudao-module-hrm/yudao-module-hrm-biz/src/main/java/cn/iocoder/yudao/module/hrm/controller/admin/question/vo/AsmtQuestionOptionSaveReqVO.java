package cn.iocoder.yudao.module.hrm.controller.admin.question.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

@Schema(description = "管理后台 - 测评题选项新增/修改 Request VO")
@Data
public class AsmtQuestionOptionSaveReqVO {

    @Schema(description = "选项id", requiredMode = Schema.RequiredMode.REQUIRED, example = "29569")
    private Long id;

    @Schema(description = "所属题目", example = "18085")
    private Long quseId;

    @Schema(description = "选项编码")
    private String optionCode;

    @Schema(description = "选项")
    private String optionName;

    @Schema(description = "得分")
    private Double score;

    @Schema(description = "显示顺序")
    private Integer sort;

}
