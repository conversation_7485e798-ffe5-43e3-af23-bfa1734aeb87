package cn.iocoder.yudao.module.hrm.dal.dataobject.task;

import cn.iocoder.yudao.framework.mybatis.core.dataobject.BaseDO;
import com.baomidou.mybatisplus.annotation.KeySequence;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.*;

import java.time.LocalDateTime;

/**
 * 测评任务 DO
 *
 * <AUTHOR>
 */
@TableName("hrm_asmt_task")
@KeySequence("hrm_asmt_task_seq") // 用于 Oracle、PostgreSQL、Kingbase、DB2、H2 数据库的主键自增。如果是 MySQL 等数据库，可不写。
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class AsmtTaskDO extends BaseDO {

    /**
     * 标签id
     */
    @TableId
    private Long id;
    /**
     * 任务名称
     */
    private String taskName;
    /**
     * 开始时间
     */
    private LocalDateTime beginTs;
    /**
     * 结束时间
     */
    private LocalDateTime endTs;
    /**
     * 测评包id
     */
    private Long pkgId;
    /**
     * 测评报告模板id
     */
    private Long reportTplId;
    /**
     * 开放报告
     *
     * 枚举 {@link TODO infra_boolean_string 对应的类}
     */
    private Boolean openReport;
    /**
     * 任务描述
     */
    private String description;
    /**
     * 标签
     */
    private String tags;
    /**
     * 状态
     *
     * 枚举 {@link TODO common_status 对应的类}
     */
    private Integer status; //

}
