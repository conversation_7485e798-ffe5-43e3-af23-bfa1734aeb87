package cn.iocoder.yudao.module.hrm.service.task;

import cn.hutool.core.util.StrUtil;
import cn.iocoder.yudao.framework.common.pojo.PageResult;
import cn.iocoder.yudao.framework.common.util.object.BeanUtils;
import cn.iocoder.yudao.module.hrm.controller.admin.packages.vo.AsmtPackagePaperRespVO;
import cn.iocoder.yudao.module.hrm.controller.admin.packages.vo.AsmtPackageRespVO;
import cn.iocoder.yudao.module.hrm.controller.admin.task.vo.*;
import cn.iocoder.yudao.module.hrm.dal.dataobject.packages.AsmtPackageDO;
import cn.iocoder.yudao.module.hrm.dal.dataobject.task.AsmtTaskDO;
import cn.iocoder.yudao.module.hrm.dal.dataobject.task.AsmtTaskUserDO;
import cn.iocoder.yudao.module.hrm.dal.mysql.task.AsmtTaskUserMapper;
import cn.iocoder.yudao.module.hrm.service.answersheet.AsmtAnswersheetService;
import cn.iocoder.yudao.module.hrm.service.packages.AsmtPackagePaperService;
import cn.iocoder.yudao.module.hrm.service.packages.AsmtPackageService;
import cn.iocoder.yudao.module.infra.api.config.ConfigApi;
import cn.iocoder.yudao.module.system.api.permission.PermissionApi;
import cn.iocoder.yudao.module.system.api.user.AdminUserApi;
import cn.iocoder.yudao.module.system.api.user.dto.UserAddReqDTO;
import jakarta.annotation.Resource;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.validation.annotation.Validated;

import java.time.LocalDateTime;
import java.util.HashSet;
import java.util.List;
import java.util.Set;
import java.util.stream.Collectors;

import static cn.iocoder.yudao.framework.common.exception.enums.GlobalErrorCodeConstants.UNAUTHORIZED;
import static cn.iocoder.yudao.framework.common.exception.util.ServiceExceptionUtil.exception;
import static cn.iocoder.yudao.framework.security.core.util.SecurityFrameworkUtils.getLoginUserId;
import static cn.iocoder.yudao.framework.security.core.util.SecurityFrameworkUtils.getLoginUserNickname;
import static cn.iocoder.yudao.module.hrm.enums.ErrorCodeConstants.*;
import static cn.iocoder.yudao.module.system.enums.ErrorCodeConstants.USER_IMPORT_INIT_PASSWORD;

/**
 * 测评任务-任务用户关联 Service 实现类
 *
 * <AUTHOR>
 */
@Service
@Validated
public class AsmtTaskUserServiceImpl implements AsmtTaskUserService {

    @Resource
    private AsmtTaskUserMapper asmtTaskUserMapper;

    @Resource
    private AsmtTaskService taskService;

    @Resource
    private AsmtPackageService asmtPackageService;

    @Resource
    private AsmtPackagePaperService packagePaperService;

    @Resource
    private AsmtAnswersheetService asmtAnswersheetService;

    @Resource
    private ConfigApi configApi;

    @Resource
    private AdminUserApi adminUserApi;

    @Resource
    private PermissionApi permissionApi;

    @Override
    public Long createAsmtTaskUser(AsmtTaskUserSaveReqVO createReqVO) {
        // 插入
        AsmtTaskUserDO asmtTaskUser = BeanUtils.toBean(createReqVO, AsmtTaskUserDO.class);
        asmtTaskUserMapper.insert(asmtTaskUser);
        // 返回
        return asmtTaskUser.getId();
    }

    @Override
    public void createAsmtTaskUserBatch(List<AsmtTaskUserSaveReqVO> list) {
        for (AsmtTaskUserSaveReqVO reqVO : list) {
            if (reqVO.getStatus() == null) {
                reqVO.setStatus(0);
            }
            // 查重
            AsmtTaskUserDO userDO = asmtTaskUserMapper.getByUserIdAndTaskId(reqVO.getUserId(), reqVO.getTaskId());
            if (userDO == null) {
                // 插入
                AsmtTaskUserDO asmtTaskUser = BeanUtils.toBean(reqVO, AsmtTaskUserDO.class);
                asmtTaskUserMapper.insert(asmtTaskUser);
            }
        }
    }

    @Override
    public void updateAsmtTaskUser(AsmtTaskUserSaveReqVO updateReqVO) {
        // 校验存在
        validateAsmtTaskUserExists(updateReqVO.getId());
        // 更新
        AsmtTaskUserDO updateObj = BeanUtils.toBean(updateReqVO, AsmtTaskUserDO.class);
        asmtTaskUserMapper.updateById(updateObj);
    }

    @Override
    public void deleteAsmtTaskUser(Long id) {
        // 校验存在
        validateAsmtTaskUserExists(id);
        // 删除
        asmtTaskUserMapper.deleteById(id);
    }

    @Override
    public Long getUserCountByTaskId(Long taskId) {
        return asmtTaskUserMapper.getUserCountByTaskId(taskId);
    }

    private void validateAsmtTaskUserExists(Long id) {
        if (asmtTaskUserMapper.selectById(id) == null) {
            throw exception(ASMT_TASK_USER_NOT_EXISTS);
        }
    }

    @Override
    public AsmtTaskUserDO getAsmtTaskUser(Long id) {
        return asmtTaskUserMapper.selectById(id);
    }

    @Override
    public Object getUserTaskDetail(Long id) {
        Long userId = getLoginUserId();
        if (userId == null) {
            throw exception(UNAUTHORIZED);
        }

        // 检测答题任务
        AsmtTaskUserDO taskUserDO = asmtTaskUserMapper.selectById(id);
        if (taskUserDO == null || !userId.equals(taskUserDO.getUserId())) {
            throw exception(ASMT_TASK_USER_NOT_EXISTS);
//            throw exception(ASMT_TASK_USER_DONE);
        }

        // 获取任务信息
        AsmtTaskDO taskDO = taskService.getAsmtTask(taskUserDO.getTaskId());
        // 检测任务及其状态,1为已发布
        if (taskDO == null || 1 != taskDO.getStatus()) {
            throw exception(ASMT_TASK_NOT_EXISTS);
        }

        // 检测任务时效
        LocalDateTime now = LocalDateTime.now();
        if (now.isBefore(taskDO.getBeginTs())) {
            throw exception(ASMT_TASK_USER_NOT_START);
        }
        if (now.isAfter(taskDO.getEndTs())) {
            throw exception(ASMT_TASK_USER_END);
        }

        AsmtUserTaskDetailRespVO respVO = BeanUtils.toBean(taskUserDO, AsmtUserTaskDetailRespVO.class);
        respVO.setNickname(getLoginUserNickname());
        respVO.setTaskDetail(taskDO);

        // 获取测试包
        AsmtPackageDO packageDO = asmtPackageService.getAsmtPackage(taskDO.getPkgId());
        if (packageDO == null || 1 == packageDO.getStatus()) {
            throw exception(ASMT_PACKAGE_NOT_EXISTS);
        }
        AsmtPackageRespVO packageRespVO = BeanUtils.toBean(packageDO, AsmtPackageRespVO.class);
        // 获取试卷
        List<AsmtPackagePaperRespVO> paperList = packagePaperService.getListByPkgId(packageRespVO.getId());
        // 获取答题状态
        for (AsmtPackagePaperRespVO paper : paperList) {
            paper.setIsAnswer(asmtAnswersheetService.checkUserIsAnswer(userId, id, paper.getPkgId(), paper.getPaperId()));
        }
        packageRespVO.setPaperList(paperList);
        respVO.setPackageInfo(packageRespVO);

        return respVO;
    }

    @Override
    public PageResult<AsmtTaskUserDO> getAsmtTaskUserPage(AsmtTaskUserPageReqVO pageReqVO) {
        return asmtTaskUserMapper.selectPage(pageReqVO);
    }

    @Override
    public PageResult<AsmtTaskUserRespVO> searchTaskUserPage(AsmtMyTaskPageReqVO pageReqVO) {
        if (pageReqVO.getPageNo() > 0) {
            pageReqVO.setPageNo(pageReqVO.getPageNo() - 1);
        }
        return new PageResult<>(
                asmtTaskUserMapper.searchPage(pageReqVO),
                asmtTaskUserMapper.searchCount(pageReqVO)
        );
    }

    @Override
    public List<AsmtTaskUserRespVO> getTaskUserListByTaskId(Long taskId, boolean isAnswer) {
        AsmtMyTaskPageReqVO reqVO = new  AsmtMyTaskPageReqVO();
        reqVO.setTaskId(taskId);
        reqVO.setPageNo(0);
        reqVO.setPageSize(10000);

        List<AsmtTaskUserRespVO> list = asmtTaskUserMapper.searchPage(reqVO);
        List<Long> userIds = asmtAnswersheetService.getAllUserIdsByTaskId(taskId);
        for (AsmtTaskUserRespVO respVO : list) {
            respVO.setIsAnswer(userIds.contains(respVO.getUserId()));
        }

        if (isAnswer) {
            return list.stream().filter(AsmtTaskUserRespVO::getIsAnswer).collect(Collectors.toList());
        }

        return list;
    }

    @Override
    public PageResult<AsmtTaskUserRespVO> getMyTaskList(AsmtMyTaskPageReqVO reqVO) {
        Long userId = getLoginUserId();
        if (userId == null) {
            throw exception(UNAUTHORIZED);
        }
        if (reqVO == null) {
            reqVO = new AsmtMyTaskPageReqVO();
            reqVO.setPageNo(1);
            reqVO.setPageSize(1);
            reqVO.setTaskStatus(1);
        }
        reqVO.setUserId(userId);
        if (reqVO.getPageNo() > 0) {
            reqVO.setPageNo(reqVO.getPageNo() - 1);
        }
        PageResult<AsmtTaskUserRespVO> result = new PageResult<>(
                asmtTaskUserMapper.searchPage(reqVO),
                asmtTaskUserMapper.searchCount(reqVO)
        );

        for (AsmtTaskUserRespVO vo : result.getList()) {
            // 获取试卷
            List<AsmtPackagePaperRespVO> paperList = packagePaperService.getListByPkgId(vo.getPkgId());
            // 获取答题状态
            for (AsmtPackagePaperRespVO paper : paperList) {
                paper.setIsAnswer(asmtAnswersheetService.checkUserIsAnswer(userId, vo.getId(), paper.getPkgId(), paper.getPaperId()));
            }
            vo.setPaperList(paperList);
        }

        return result;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Long inviteTaskUser(AsmtTaskUserInviteReqVO reqVO) {
        if (reqVO.getTaskId() == null) {
            throw exception(ASMT_TASK_NOT_EXISTS);
        }
        String orgId = configApi.getConfigValueByKey("task-invited-org-id"); // 部门ID
        String roleId = configApi.getConfigValueByKey("task-invited-role-id"); // 角色ID

        UserAddReqDTO reqDTO = BeanUtils.toBean(reqVO, UserAddReqDTO.class);
        if (StrUtil.isNotEmpty(orgId)) {
            reqDTO.setDeptId(Long.parseLong(orgId));
        }


        // 1.2 初始化密码不能为空
        String initPassword = configApi.getConfigValueByKey("system.user.init-password");
        if (StrUtil.isEmpty(initPassword)) {
            throw exception(USER_IMPORT_INIT_PASSWORD);
        }

        reqDTO.setPassword(initPassword);
        reqDTO.setUsername(reqVO.getMobile());

        // 插入新用户
        Long userId = adminUserApi.createUser(reqDTO);

        if (StrUtil.isNotEmpty(roleId)) {
            Set<Long> set = new HashSet<>();
            set.add(Long.parseLong(roleId));
            permissionApi.assignUserRole(userId, set);
        }

        AsmtTaskUserDO userDO = new AsmtTaskUserDO();
        userDO.setUserId(userId);
        userDO.setTaskId(reqVO.getTaskId());
        userDO.setStatus(0);
        asmtTaskUserMapper.insert(userDO);
        return userDO.getId();
    }
}
