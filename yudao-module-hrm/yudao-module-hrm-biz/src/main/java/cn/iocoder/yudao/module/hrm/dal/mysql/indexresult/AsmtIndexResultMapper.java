package cn.iocoder.yudao.module.hrm.dal.mysql.indexresult;

import cn.iocoder.yudao.framework.common.pojo.PageResult;
import cn.iocoder.yudao.framework.mybatis.core.mapper.BaseMapperX;
import cn.iocoder.yudao.framework.mybatis.core.query.LambdaQueryWrapperX;
import cn.iocoder.yudao.module.hrm.controller.admin.indexresult.vo.AsmtIndexResultPageReqVO;
import cn.iocoder.yudao.module.hrm.dal.dataobject.indexresult.AsmtIndexResultDO;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import org.apache.ibatis.annotations.Delete;
import org.apache.ibatis.annotations.Mapper;

import java.util.List;

/**
 * 测评指标计算结果 Mapper
 *
 * <AUTHOR>
 */
@Mapper
public interface AsmtIndexResultMapper extends BaseMapperX<AsmtIndexResultDO> {

    default PageResult<AsmtIndexResultDO> selectPage(AsmtIndexResultPageReqVO reqVO) {
        return selectPage(reqVO, new LambdaQueryWrapperX<AsmtIndexResultDO>()
                .eqIfPresent(AsmtIndexResultDO::getUserId, reqVO.getUserId())
                .eqIfPresent(AsmtIndexResultDO::getTaskId, reqVO.getTaskId())
                .eqIfPresent(AsmtIndexResultDO::getModelId, reqVO.getModelId())
                .eqIfPresent(AsmtIndexResultDO::getIndexId, reqVO.getIndexId())
                .eqIfPresent(AsmtIndexResultDO::getIsImport, reqVO.getIsImport())
                .eqIfPresent(AsmtIndexResultDO::getDescription, reqVO.getDescription())
                .orderByAsc(AsmtIndexResultDO::getId));
    }

    default AsmtIndexResultDO getByUerIdAndTaskIdAndModelIdAndIndexId(Long userId, Long taskId, Long modelId, Long indexId) {
        return selectOne(
                new LambdaQueryWrapper<AsmtIndexResultDO>()
                        .eq(AsmtIndexResultDO::getUserId, userId)
                        .eq(AsmtIndexResultDO::getTaskId, taskId)
                        .eq(AsmtIndexResultDO::getModelId, modelId)
                        .eq(AsmtIndexResultDO::getIndexId, indexId)
        );
    }

    @Delete("delete from hrm_asmt_index_result where task_id = #{taskId} and user_id = #{userId} and is_import = 0")
    void deleteByTaskIdAndUserId(Long taskId, Long userId);

    default List<AsmtIndexResultDO> selectImportListByTaskUser(Long taskId, Long userId){
        return selectList(
                new LambdaQueryWrapper<AsmtIndexResultDO>()
                        .eq(AsmtIndexResultDO::getTaskId, taskId)
                        .eq(AsmtIndexResultDO::getUserId, userId)
                        .eq(AsmtIndexResultDO::getIsImport, 1)
        );
    }
}
