package cn.iocoder.yudao.module.hrm.controller.admin.task;

import cn.iocoder.yudao.framework.apilog.core.annotation.ApiAccessLog;
import cn.iocoder.yudao.framework.common.pojo.CommonResult;
import cn.iocoder.yudao.framework.common.pojo.PageParam;
import cn.iocoder.yudao.framework.common.pojo.PageResult;
import cn.iocoder.yudao.framework.common.util.object.BeanUtils;
import cn.iocoder.yudao.framework.excel.core.util.ExcelUtils;
import cn.iocoder.yudao.module.hrm.controller.admin.task.vo.*;
import cn.iocoder.yudao.module.hrm.dal.dataobject.task.AsmtTaskUserDO;
import cn.iocoder.yudao.module.hrm.service.task.AsmtTaskUserService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.annotation.Resource;
import jakarta.servlet.http.HttpServletResponse;
import jakarta.validation.Valid;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import java.io.IOException;
import java.util.List;

import static cn.iocoder.yudao.framework.apilog.core.enums.OperateTypeEnum.EXPORT;
import static cn.iocoder.yudao.framework.common.pojo.CommonResult.success;

@Tag(name = "管理后台 - 测评任务-任务用户关联")
@RestController
@RequestMapping("/hrm/asmt-task-user")
@Validated
public class AsmtTaskUserController {

    @Resource
    private AsmtTaskUserService asmtTaskUserService;

    @PostMapping("/create")
    @Operation(summary = "创建测评任务-任务用户关联")
    @PreAuthorize("@ss.hasPermission('hrm:asmt-task-user:create')")
    public CommonResult<Long> createAsmtTaskUser(@Valid @RequestBody AsmtTaskUserSaveReqVO createReqVO) {
        return success(asmtTaskUserService.createAsmtTaskUser(createReqVO));
    }

    @PostMapping("/invite-outside")
    @Operation(summary = "邀请新的外部用户")
    @PreAuthorize("@ss.hasPermission('hrm:asmt-task-user:create')")
    public CommonResult<Long> inviteOutsideUser(@Valid @RequestBody AsmtTaskUserInviteReqVO reqVO) {
        return success(asmtTaskUserService.inviteTaskUser(reqVO));
    }

    @PostMapping("/create-batch")
    @Operation(summary = "批量创建测评任务-任务用户关联")
    @PreAuthorize("@ss.hasPermission('hrm:asmt-task-user:create')")
    public CommonResult<Boolean> createAsmtTaskUserBatch(@Valid @RequestBody List<AsmtTaskUserSaveReqVO> list) {
        asmtTaskUserService.createAsmtTaskUserBatch(list);
        return success(true);
    }

    @PutMapping("/update")
    @Operation(summary = "更新测评任务-任务用户关联")
    @PreAuthorize("@ss.hasPermission('hrm:asmt-task-user:update')")
    public CommonResult<Boolean> updateAsmtTaskUser(@Valid @RequestBody AsmtTaskUserSaveReqVO updateReqVO) {
        asmtTaskUserService.updateAsmtTaskUser(updateReqVO);
        return success(true);
    }

    @DeleteMapping("/delete")
    @Operation(summary = "删除测评任务-任务用户关联")
    @Parameter(name = "id", description = "编号", required = true)
    @PreAuthorize("@ss.hasPermission('hrm:asmt-task-user:delete')")
    public CommonResult<Boolean> deleteAsmtTaskUser(@RequestParam("id") Long id) {
        asmtTaskUserService.deleteAsmtTaskUser(id);
        return success(true);
    }

    @GetMapping("/get")
    @Operation(summary = "获得测评任务-任务用户关联")
    @Parameter(name = "id", description = "编号", required = true, example = "1024")
    @PreAuthorize("@ss.hasPermission('hrm:asmt-task-user:query')")
    public CommonResult<AsmtTaskUserRespVO> getAsmtTaskUser(@RequestParam("id") Long id) {
        AsmtTaskUserDO asmtTaskUser = asmtTaskUserService.getAsmtTaskUser(id);
        return success(BeanUtils.toBean(asmtTaskUser, AsmtTaskUserRespVO.class));
    }

    @PostMapping("/my-task")
    @Operation(summary = "获取当前用户的任务列表")
    public CommonResult<PageResult<AsmtTaskUserRespVO>> getMyTask(@Valid @RequestBody(required = false) AsmtMyTaskPageReqVO reqVO) {
        return success(asmtTaskUserService.getMyTaskList(reqVO));
    }

    @GetMapping("/get-user-task-detail")
    @Operation(summary = "获得当前用户测评任务详情")
    @Parameter(name = "id", description = "评测任务关联表编号", required = true, example = "1024")
    public CommonResult<Object> getUserTaskDetail(@RequestParam("userTaskId") Long userTaskId) {
        return success(asmtTaskUserService.getUserTaskDetail(userTaskId));
    }

    @GetMapping("/page")
    @Operation(summary = "获得测评任务-任务用户关联分页")
    @PreAuthorize("@ss.hasPermission('hrm:asmt-task-user:query')")
    public CommonResult<PageResult<AsmtTaskUserRespVO>> getAsmtTaskUserPage(@Valid AsmtTaskUserPageReqVO pageReqVO) {
        PageResult<AsmtTaskUserDO> pageResult = asmtTaskUserService.getAsmtTaskUserPage(pageReqVO);
        return success(BeanUtils.toBean(pageResult, AsmtTaskUserRespVO.class));
    }

    @GetMapping("/search")
    @Operation(summary = "获得测评任务-任务用户关联分页")
    @PreAuthorize("@ss.hasPermission('hrm:asmt-task-user:query')")
    public CommonResult<PageResult<AsmtTaskUserRespVO>> search(@Valid AsmtMyTaskPageReqVO pageReqVO) {
        return success(asmtTaskUserService.searchTaskUserPage(pageReqVO));
    }

    @GetMapping("/list-by-task")
    @Operation(summary = "根据任务ID获取用户列表")
    @Parameter(name = "taskId", description = "任务ID", required = true, example = "1024")
    @Parameter(name = "isAnswer", description = "是否只查询已作答的", required = true, example = "true")
    @PreAuthorize("@ss.hasPermission('hrm:asmt-task-user:query')")
    public CommonResult<List<AsmtTaskUserRespVO>> search(@RequestParam Long taskId,
                                                         @RequestParam Boolean isAnswer) {
        return success(asmtTaskUserService.getTaskUserListByTaskId(taskId, isAnswer));
    }

    @GetMapping("/export-excel")
    @Operation(summary = "导出测评任务-任务用户关联 Excel")
    @PreAuthorize("@ss.hasPermission('hrm:asmt-task-user:export')")
    @ApiAccessLog(operateType = EXPORT)
    public void exportAsmtTaskUserExcel(@Valid AsmtTaskUserPageReqVO pageReqVO,
              HttpServletResponse response) throws IOException {
        pageReqVO.setPageSize(PageParam.PAGE_SIZE_NONE);
        List<AsmtTaskUserDO> list = asmtTaskUserService.getAsmtTaskUserPage(pageReqVO).getList();
        // 导出 Excel
        ExcelUtils.write(response, "测评任务-任务用户关联.xls", "数据", AsmtTaskUserRespVO.class,
                        BeanUtils.toBean(list, AsmtTaskUserRespVO.class));
    }
}
