package cn.iocoder.yudao.module.hrm.dal.dataobject.model;

import cn.iocoder.yudao.framework.mybatis.core.dataobject.BaseDO;
import com.baomidou.mybatisplus.annotation.KeySequence;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.*;

/**
 * 测评指标 DO
 *
 * <AUTHOR>
 */
@TableName("hrm_asmt_index")
@KeySequence("hrm_asmt_index_seq") // 用于 Oracle、PostgreSQL、Kingbase、DB2、H2 数据库的主键自增。如果是 MySQL 等数据库，可不写。
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class AsmtIndexDO extends BaseDO {

    /**
     * 标签id
     */
    @TableId
    private Long id;
    /**
     * 指标名称
     */
    private String indexName;
    /**
     * 指标编码
     */
    private String indexCode;
    /**
     * 组名
     */
    private String indexGroup;
    /**
     * 模型id
     */
    private Long modelId;
    /**
     * 指标级别
     */
    private Integer level;

    /**
     * 标准分转换方式（0-不需要，1-区间映射表，2-公式）
     */
    private Integer coverType;
    /**
     * 计算规则表达式
     */
    private String rule;
    /**
     * 标准分计算规则表达式
     */
    private String coverRule;
    /**
     * 说明
     */
    private String description;
    /**
     * 是否导入指标
     */
    private Boolean isImport;
    /**
     * 状态
     */
    private Integer status;
    /**
     * 显示顺序
     */
    private Integer sort;
}
