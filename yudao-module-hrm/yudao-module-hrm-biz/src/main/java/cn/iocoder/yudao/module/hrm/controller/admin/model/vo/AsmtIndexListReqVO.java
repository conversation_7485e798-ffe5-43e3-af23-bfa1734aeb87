package cn.iocoder.yudao.module.hrm.controller.admin.model.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.ToString;

@Schema(description = "管理后台 - 指标列表 Request VO")
@Data
@ToString(callSuper = true)
public class AsmtIndexListReqVO {

    private String indexName;
    private String indexGroup;

    private Long modelId;

    private Integer level;
    private Boolean isImport;
    private Integer status;

}
