package cn.iocoder.yudao.module.hrm.controller.admin.question.vo;

import com.alibaba.excel.annotation.ExcelIgnoreUnannotated;
import com.alibaba.excel.annotation.ExcelProperty;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.time.LocalDateTime;

@Schema(description = "管理后台 - 测评题选项 Response VO")
@Data
@ExcelIgnoreUnannotated
public class AsmtQuestionOptionRespVO {

    @Schema(description = "选项id", requiredMode = Schema.RequiredMode.REQUIRED, example = "29569")
    @ExcelProperty("选项id")
    private Long id;

    @Schema(description = "所属题目", example = "18085")
    @ExcelProperty("所属题目")
    private Long quseId;

    @Schema(description = "选项编码")
    @ExcelProperty("选项编码")
    private String optionCode;

    @Schema(description = "选项")
    @ExcelProperty("选项")
    private String optionName;

    @Schema(description = "得分")
    @ExcelProperty("得分")
    private Double score;

    @Schema(description = "显示顺序")
    @ExcelProperty("显示顺序")
    private Integer sort;

    @Schema(description = "创建时间")
    @ExcelProperty("创建时间")
    private LocalDateTime createTime;

}