package cn.iocoder.yudao.module.hrm.service.question;

import cn.hutool.json.JSONObject;
import cn.iocoder.yudao.framework.common.pojo.PageResult;
import cn.iocoder.yudao.module.hrm.controller.admin.question.vo.AsmtQuestionEditReqVO;
import cn.iocoder.yudao.module.hrm.controller.admin.question.vo.AsmtQuestionPageReqVO;
import cn.iocoder.yudao.module.hrm.dal.dataobject.question.AsmtQuestionDO;
import jakarta.validation.Valid;

/**
 * 测评题 Service 接口
 *
 * <AUTHOR>
 */
public interface AsmtQuestionService {

    /**
     * 创建测评题
     *
     * @param reqVO 创建信息
     * @return 编号
     */
    Long createAsmtQuestion(@Valid AsmtQuestionEditReqVO reqVO);

    /**
     * 更新测评题
     *
     * @param updateReqVO 更新信息
     */
    void updateAsmtQuestion(@Valid AsmtQuestionEditReqVO updateReqVO);

    /**
     * 删除测评题
     *
     * @param id 编号
     */
    void deleteAsmtQuestion(Long id);

    /**
     * 获得测评题
     *
     * @param id 编号
     * @return 测评题
     */
    AsmtQuestionDO getAsmtQuestion(Long id);

    /**
     * 获得测评题与相应选项
     *
     * @param id 编号
     * @return 测评题及选项
     */
    JSONObject getAsmtQuestionDetail(Long id);

    /**
     * 获得测评题分页
     *
     * @param pageReqVO 分页查询
     * @return 测评题分页
     */
    PageResult<AsmtQuestionDO> getAsmtQuestionPage(AsmtQuestionPageReqVO pageReqVO);

    /**
     * 获得测评题分页（ID升序）
     *
     * @param pageReqVO 分页查询
     * @return 测评题分页
     */
    PageResult<AsmtQuestionDO> getAsmtQuestionPageOrderById(@Valid AsmtQuestionPageReqVO pageReqVO);
}
