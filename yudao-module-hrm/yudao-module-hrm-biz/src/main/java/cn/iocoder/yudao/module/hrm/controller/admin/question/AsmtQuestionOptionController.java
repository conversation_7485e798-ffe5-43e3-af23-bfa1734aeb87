package cn.iocoder.yudao.module.hrm.controller.admin.question;

import cn.iocoder.yudao.framework.apilog.core.annotation.ApiAccessLog;
import cn.iocoder.yudao.framework.common.pojo.CommonResult;
import cn.iocoder.yudao.framework.common.pojo.PageParam;
import cn.iocoder.yudao.framework.common.pojo.PageResult;
import cn.iocoder.yudao.framework.common.util.object.BeanUtils;
import cn.iocoder.yudao.framework.excel.core.util.ExcelUtils;
import cn.iocoder.yudao.module.hrm.controller.admin.question.vo.AsmtQuestionOptionPageReqVO;
import cn.iocoder.yudao.module.hrm.controller.admin.question.vo.AsmtQuestionOptionRespVO;
import cn.iocoder.yudao.module.hrm.controller.admin.question.vo.AsmtQuestionOptionSaveReqVO;
import cn.iocoder.yudao.module.hrm.dal.dataobject.question.AsmtQuestionOptionDO;
import cn.iocoder.yudao.module.hrm.service.question.AsmtQuestionOptionService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.annotation.Resource;
import jakarta.servlet.http.HttpServletResponse;
import jakarta.validation.Valid;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import java.io.IOException;
import java.util.List;

import static cn.iocoder.yudao.framework.apilog.core.enums.OperateTypeEnum.EXPORT;
import static cn.iocoder.yudao.framework.common.pojo.CommonResult.success;

@Tag(name = "管理后台 - 测评题选项")
@RestController
@RequestMapping("/hrm/asmt-question-option")
@Validated
public class AsmtQuestionOptionController {

    @Resource
    private AsmtQuestionOptionService asmtQuestionOptionService;

    @PostMapping("/create")
    @Operation(summary = "创建测评题选项")
    @PreAuthorize("@ss.hasPermission('hrm:asmt-question-option:create')")
    public CommonResult<Long> createAsmtQuestionOption(@Valid @RequestBody AsmtQuestionOptionSaveReqVO createReqVO) {
        return success(asmtQuestionOptionService.createAsmtQuestionOption(createReqVO));
    }

    @PutMapping("/update")
    @Operation(summary = "更新测评题选项")
    @PreAuthorize("@ss.hasPermission('hrm:asmt-question-option:update')")
    public CommonResult<Boolean> updateAsmtQuestionOption(@Valid @RequestBody AsmtQuestionOptionSaveReqVO updateReqVO) {
        asmtQuestionOptionService.updateAsmtQuestionOption(updateReqVO);
        return success(true);
    }

    @PutMapping("/update-sorts")
    @Operation(summary = "批量更新显示顺序")
    @PreAuthorize("@ss.hasPermission('hrm:asmt-question-option:update')")
    public CommonResult<Boolean> updateAsmtQuestionOptionSorts(@Valid @RequestBody List<AsmtQuestionOptionSaveReqVO> list) {
        asmtQuestionOptionService.updateAsmtQuestionOptionSorts(list);
        return success(true);
    }

    @DeleteMapping("/delete")
    @Operation(summary = "删除测评题选项")
    @Parameter(name = "id", description = "编号", required = true)
    @PreAuthorize("@ss.hasPermission('hrm:asmt-question-option:delete')")
    public CommonResult<Boolean> deleteAsmtQuestionOption(@RequestParam("id") Long id) {
        asmtQuestionOptionService.deleteAsmtQuestionOption(id);
        return success(true);
    }

    @GetMapping("/get")
    @Operation(summary = "获得测评题选项")
    @Parameter(name = "id", description = "编号", required = true, example = "1024")
    @PreAuthorize("@ss.hasPermission('hrm:asmt-question-option:query')")
    public CommonResult<AsmtQuestionOptionRespVO> getAsmtQuestionOption(@RequestParam("id") Long id) {
        AsmtQuestionOptionDO asmtQuestionOption = asmtQuestionOptionService.getAsmtQuestionOption(id);
        return success(BeanUtils.toBean(asmtQuestionOption, AsmtQuestionOptionRespVO.class));
    }

    @GetMapping("/get-by-queid")
    @Operation(summary = "获得测评题选项")
    @Parameter(name = "id", description = "编号", required = true, example = "1024")
    @PreAuthorize("@ss.hasPermission('hrm:asmt-question-option:query')")
    public CommonResult<Object> getAsmtQuestionOptionByQueId(@RequestParam("id") Long id) {
        return success(asmtQuestionOptionService.getAsmtQuestionOptionList(id));
    }

    @GetMapping("/page")
    @Operation(summary = "获得测评题选项分页")
    @PreAuthorize("@ss.hasPermission('hrm:asmt-question-option:query')")
    public CommonResult<PageResult<AsmtQuestionOptionRespVO>> getAsmtQuestionOptionPage(@Valid AsmtQuestionOptionPageReqVO pageReqVO) {
        PageResult<AsmtQuestionOptionDO> pageResult = asmtQuestionOptionService.getAsmtQuestionOptionPage(pageReqVO);
        return success(BeanUtils.toBean(pageResult, AsmtQuestionOptionRespVO.class));
    }

    @GetMapping("/export-excel")
    @Operation(summary = "导出测评题选项 Excel")
    @PreAuthorize("@ss.hasPermission('hrm:asmt-question-option:export')")
    @ApiAccessLog(operateType = EXPORT)
    public void exportAsmtQuestionOptionExcel(@Valid AsmtQuestionOptionPageReqVO pageReqVO,
              HttpServletResponse response) throws IOException {
        pageReqVO.setPageSize(PageParam.PAGE_SIZE_NONE);
        List<AsmtQuestionOptionDO> list = asmtQuestionOptionService.getAsmtQuestionOptionPage(pageReqVO).getList();
        // 导出 Excel
        ExcelUtils.write(response, "测评题选项.xls", "数据", AsmtQuestionOptionRespVO.class,
                        BeanUtils.toBean(list, AsmtQuestionOptionRespVO.class));
    }

}
