package cn.iocoder.yudao.module.hrm.service.task;

import cn.iocoder.yudao.framework.common.pojo.PageResult;
import cn.iocoder.yudao.module.hrm.controller.admin.task.vo.AsmtTaskPageReqVO;
import cn.iocoder.yudao.module.hrm.controller.admin.task.vo.AsmtTaskRespVO;
import cn.iocoder.yudao.module.hrm.controller.admin.task.vo.AsmtTaskSaveReqVO;
import cn.iocoder.yudao.module.hrm.dal.dataobject.task.AsmtTaskDO;
import jakarta.validation.Valid;

/**
 * 测评任务 Service 接口
 *
 * <AUTHOR>
 */
public interface AsmtTaskService {

    /**
     * 创建测评任务
     *
     * @param createReqVO 创建信息
     * @return 编号
     */
    Long createAsmtTask(@Valid AsmtTaskSaveReqVO createReqVO);

    /**
     * 更新测评任务
     *
     * @param updateReqVO 更新信息
     */
    void updateAsmtTask(@Valid AsmtTaskSaveReqVO updateReqVO);
    void updateAsmtTaskStatus(Long id, Integer status);

    /**
     * 删除测评任务
     *
     * @param id 编号
     */
    void deleteAsmtTask(Long id);

    /**
     * 获得测评任务
     *
     * @param id 编号
     * @return 测评任务
     */
    AsmtTaskDO getAsmtTask(Long id);

    /**
     * 获得测评任务分页
     *
     * @param pageReqVO 分页查询
     * @return 测评任务分页
     */
    PageResult<AsmtTaskRespVO> getAsmtTaskPage(AsmtTaskPageReqVO pageReqVO);

    PageResult<AsmtTaskDO> getAsmtMyTaskPage(AsmtTaskPageReqVO pageReqVO, Long userId);

}
