package cn.iocoder.yudao.module.hrm.controller.admin.paper.vo;

import cn.iocoder.yudao.framework.common.pojo.PageParam;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.ToString;

@Schema(description = "管理后台 - 测评卷分页 Request VO")
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
public class AsmtPaperPageReqVO extends PageParam {

    @Schema(description = "试卷编码")
    private String paperCode;

    @Schema(description = "试卷名称", example = "芋艿")
    private String paperName;

    @Schema(description = "测评类型", example = "1")
    private String asmtType;

    @Schema(description = "试卷说明", example = "你说的对")
    private String description;

    @Schema(description = "标签")
    private String tags;

    @Schema(description = "状态", example = "1")
    private Integer status;

}