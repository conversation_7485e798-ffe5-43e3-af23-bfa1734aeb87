package cn.iocoder.yudao.module.hrm.service.model;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.StrUtil;
import cn.iocoder.yudao.framework.common.exception.ErrorCode;
import cn.iocoder.yudao.framework.common.pojo.PageResult;
import cn.iocoder.yudao.framework.common.util.object.BeanUtils;
import cn.iocoder.yudao.module.hrm.controller.admin.model.vo.*;
import cn.iocoder.yudao.module.hrm.controller.admin.packages.vo.AsmtPackagePaperRespVO;
import cn.iocoder.yudao.module.hrm.controller.admin.paper.vo.AsmtPaperQuestionRespVO;
import cn.iocoder.yudao.module.hrm.dal.dataobject.indexresult.AsmtIndexResultDO;
import cn.iocoder.yudao.module.hrm.dal.dataobject.model.AsmtIndexDO;
import cn.iocoder.yudao.module.hrm.dal.dataobject.model.AsmtModelDO;
import cn.iocoder.yudao.module.hrm.dal.mysql.indexresult.AsmtIndexResultMapper;
import cn.iocoder.yudao.module.hrm.dal.mysql.model.AsmtIndexMapper;
import cn.iocoder.yudao.module.hrm.dal.mysql.model.AsmtModelMapper;
import cn.iocoder.yudao.module.hrm.service.packages.AsmtPackagePaperService;
import cn.iocoder.yudao.module.hrm.service.paper.AsmtPaperQuestionService;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import jakarta.annotation.Resource;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.validation.annotation.Validated;

import java.util.*;
import java.util.stream.Collectors;

import static cn.iocoder.yudao.framework.common.exception.util.ServiceExceptionUtil.exception;
import static cn.iocoder.yudao.module.hrm.enums.ErrorCodeConstants.*;

/**
 * 测评模型 Service 实现类
 *
 * <AUTHOR>
 */
@Service
@Validated
public class AsmtModelServiceImpl implements AsmtModelService {

    @Resource
    private AsmtModelMapper asmtModelMapper;
    @Resource
    private AsmtIndexMapper asmtIndexMapper;

    @Resource
    private AsmtPackagePaperService packagePaperService;

    @Resource
    private AsmtPaperQuestionService asmtPaperQuestionService;

    @Resource
    private AsmtIndexResultMapper indexResultMapper;


    @Override
    public Long createAsmtModel(AsmtModelSaveReqVO createReqVO) {
        // 插入
        AsmtModelDO asmtModel = BeanUtils.toBean(createReqVO, AsmtModelDO.class);
        asmtModelMapper.insert(asmtModel);
        // 返回
        return asmtModel.getId();
    }

    @Override
    public void updateAsmtModel(AsmtModelSaveReqVO updateReqVO) {
        // 校验存在
        validateAsmtModelExists(updateReqVO.getId());
        // 更新
        AsmtModelDO updateObj = BeanUtils.toBean(updateReqVO, AsmtModelDO.class);
        asmtModelMapper.updateById(updateObj);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void copyAsmtModel(Long modelId) {
        AsmtModelDO modelDO = getAsmtModel(modelId);
        AsmtModelSaveReqVO saveReqVO = BeanUtils.toBean(modelDO, AsmtModelSaveReqVO.class);
        saveReqVO.setId(null);
        saveReqVO.setModelName(modelDO.getModelName() + "-copy");
        Long newModelId = createAsmtModel(saveReqVO);

        AsmtIndexListReqVO reqVO = new AsmtIndexListReqVO();
        reqVO.setModelId(modelId);
        List<AsmtIndexDO> indexDOList = getIndexList(reqVO);
        List<AsmtIndexSaveReqVO> saveReqVOList = BeanUtils.toBean(indexDOList, AsmtIndexSaveReqVO.class);
        for (AsmtIndexSaveReqVO vo : saveReqVOList) {
            vo.setId(null);
            vo.setModelId(newModelId);
            asmtIndexMapper.insert(BeanUtils.toBean(vo, AsmtIndexDO.class));
        }

    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void deleteAsmtModel(Long id) {
        // 校验存在
        validateAsmtModelExists(id);
        // 删除
        asmtModelMapper.deleteById(id);

        // 删除子表
        // deleteAsmtIndexByModelId(id);
    }

    private void validateAsmtModelExists(Long id) {
        if (asmtModelMapper.selectById(id) == null) {
            throw exception(ASMT_MODEL_NOT_EXISTS);
        }
    }

    @Override
    public Object getModelOriginalIndex(Long id) {
        AsmtModelDO asmtModel = getAsmtModel(id);
        if (asmtModel == null) {
            throw exception(ASMT_MODEL_NOT_EXISTS);
        }

        // 1. 获取测评包的试卷列表
        List<AsmtPackagePaperRespVO> paperList = packagePaperService.getListByPkgId(asmtModel.getPkgId());

        // 2. 遍历获取试卷关联的题目
        List<ModelOriginalIndexRespVO> res = new ArrayList<>(paperList.size());
        for (AsmtPackagePaperRespVO paper : paperList) {
            List<AsmtPaperQuestionRespVO> questionList = asmtPaperQuestionService.getAsmtPaperQuestionByPaperId(paper.getPaperId());
            if (questionList.isEmpty()) continue;
            ModelOriginalIndexRespVO vo = ModelOriginalIndexRespVO
                    .builder()
                    .paperId(paper.getPaperId())
                    .paperType(paper.getPaperType())
                    .paperName(paper.getPaperName())
                    .sort(paper.getSort())
                    .questionList(questionList)
                    .build();
            res.add(vo);
        }

        return res;
    }

    @Override
    public AsmtModelDO getAsmtModel(Long id) {
        return asmtModelMapper.selectById(id);
    }

    @Override
    public PageResult<AsmtModelDO> getAsmtModelPage(AsmtModelPageReqVO pageReqVO) {
        return asmtModelMapper.selectPage(pageReqVO);
    }

    // ==================== 子表（测评指标） ====================

    @Override
    public List<Object> getIndexGroupsByModelId(Long modelId) {
        List<Map<String, Object>> list = asmtIndexMapper.getIndexGroupsByModelId(modelId);
        return list.stream().map(m -> m.get("index_group")).collect(Collectors.toList());
    }

    @Override
    public PageResult<AsmtIndexDO> getAsmtIndexPage(AsmtIndexPageReqVO pageReqVO) {
        return asmtIndexMapper.selectPage(pageReqVO);
    }

    @Override
    public List<AsmtIndexDO> getIndexList(AsmtIndexListReqVO pageReqVO) {
        if (pageReqVO.getModelId() == null) {
            throw exception(ASMT_MODEL_NOT_EXISTS);
        }
        return asmtIndexMapper.getList(pageReqVO);
    }

    @Override
    public Object getCanUseIndex(Long id) {
        AsmtIndexDO indexDO = getAsmtIndex(id);
        if (indexDO == null) {
            throw exception(ASMT_INDEX_NOT_EXISTS);
        }

        // 获取导入指标
        AsmtIndexListReqVO importReqVO = new AsmtIndexListReqVO();
        importReqVO.setModelId(indexDO.getModelId());
        importReqVO.setIsImport(true);
        importReqVO.setStatus(0);
        List<AsmtIndexDO> importIndexList = asmtIndexMapper.getList(importReqVO);

        // 获取小于当前指标级别的指标
        List<AsmtIndexDO> indexList = asmtIndexMapper.getIndexListBelowLevel(indexDO.getModelId(), indexDO.getLevel());

        Map<String, List<AsmtIndexDO>> res = new HashMap<>();
        res.put("importIndexList", importIndexList);
        res.put("indexList", indexList);

        return res;
    }

    @Override
    public Long createAsmtIndex(AsmtIndexDO asmtIndex) {
        asmtIndexMapper.insert(asmtIndex);
        return asmtIndex.getId();
    }

    @Override
    public void updateAsmtIndex(AsmtIndexDO asmtIndex) {
        // 校验存在
        AsmtIndexDO indexDO = asmtIndexMapper.selectById(asmtIndex.getId());
        if (indexDO == null) {
            throw exception(ASMT_INDEX_NOT_EXISTS);
        }
        // 不更新规则，需要单独的接口更新
        asmtIndex.setRule(indexDO.getRule());
        asmtIndex.setCoverRule(indexDO.getCoverRule());
        // 更新
        asmtIndex.setUpdater(null).setUpdateTime(null); // 解决更新情况下：updateTime 不更新
        asmtIndexMapper.updateById(asmtIndex);
    }

    @Override
    public void updateIndexRule(AsmtIndexDO asmtIndex) {
        // TODO 添加规则有效性逻辑
        // 校验公式有效性
//        ExcelFormulaCalculator calculator = new ExcelFormulaCalculator();
//        calculator.validateFormulaWithException(asmtIndex.getRule());

        asmtIndexMapper.update(
                new LambdaUpdateWrapper<AsmtIndexDO>()
                        .set(AsmtIndexDO::getRule, asmtIndex.getRule())
                        .set(AsmtIndexDO::getCoverRule, asmtIndex.getCoverRule())
                        .eq(AsmtIndexDO::getId, asmtIndex.getId()));
    }

    @Override
    public String validateIndexRule(AsmtIndexDO asmtIndex) {
        // TODO 添加规则有效性逻辑
        return null; // 为null表示校验通过,否则返回错误原因
    }

    @Override
    public void deleteAsmtIndex(Long id) {
        // 校验存在
        validateAsmtIndexExists(id);
        // 删除
        asmtIndexMapper.deleteById(id);
    }

    @Override
    public AsmtIndexDO getAsmtIndex(Long id) {
        return asmtIndexMapper.selectById(id);
    }

    @Override
    @Transactional(rollbackFor = Exception.class) // 添加事务，异常则回滚所有导入
    public Object importIndexList(List<IndexImportExcelVO> list, Long modelId, Long taskId, Long userId) {
        // 1、参数校验
        if (CollUtil.isEmpty(list)) {
            throw exception(ASMT_INDEX_IMPORT_ENPTY);
        }

        IndexImportRespVO respVO = IndexImportRespVO.builder()
                .createNames(new ArrayList<>())
                .failureNames(new LinkedHashMap<>())
                .build();
        int i = 2;
        for (IndexImportExcelVO excelVO : list) {
            if (StrUtil.isEmpty(excelVO.getIndexName())) {
                throw exception(new ErrorCode(902, "第" + i + "行指标名称不能为空"));
            }
            if (StrUtil.isEmpty(excelVO.getIndexCode())) {
                throw exception(new ErrorCode(902, "第" + i + "行指标编码不能为空"));
            }
            // 检查是否已经定义了指标
            AsmtIndexDO indexDO = asmtIndexMapper.getByModelIdAndIndexName(modelId, excelVO.getIndexName());
            if (indexDO == null) {
                indexDO = BeanUtils.toBean(excelVO, AsmtIndexDO.class);
                indexDO.setModelId(modelId);
                indexDO.setLevel(0);
                indexDO.setIsImport(true);
                indexDO.setStatus(0);
                asmtIndexMapper.insert(indexDO);

                // 创建指标结果记录
                creatIndexResultVO(modelId, taskId, userId, excelVO, indexDO.getId());
            } else {
                // 检查指标结果是否存在，如果存在则更新，否则新建
                AsmtIndexResultDO resultDO = indexResultMapper.getByUerIdAndTaskIdAndModelIdAndIndexId(userId, taskId, modelId, indexDO.getId());
                if (resultDO == null) {
                    creatIndexResultVO(modelId, taskId, userId, excelVO, indexDO.getId());
                } else {
                    resultDO.setScore(excelVO.getScore());
                    resultDO.setStdScore(excelVO.getStdScore());
                    resultDO.setRefValue(excelVO.getRefValue());
                    resultDO.setDescription(excelVO.getDescription());
                    indexResultMapper.updateById(resultDO);
                }
            }

            i++;
        }

        return respVO;
    }

    private void creatIndexResultVO(Long modelId, Long taskId, Long userId, IndexImportExcelVO excelVO, Long indexId) {
        AsmtIndexResultDO resultDO = new AsmtIndexResultDO();
        resultDO.setUserId(userId);
        resultDO.setTaskId(taskId);
        resultDO.setIndexId(indexId);
        resultDO.setModelId(modelId);
        resultDO.setScore(excelVO.getScore());
        resultDO.setStdScore(excelVO.getStdScore());
        resultDO.setRefValue(excelVO.getRefValue());
        resultDO.setIsImport(true);
        resultDO.setDescription(excelVO.getDescription());
        indexResultMapper.insert(resultDO);
    }

    @Override
    public void updateIndexSort(List<AsmtIndexDO> list) {
        for (AsmtIndexDO asmtIndexDO : list) {
            asmtIndexMapper.update(new LambdaUpdateWrapper<AsmtIndexDO>()
                    .eq(AsmtIndexDO::getId, asmtIndexDO.getId())
                    .set(AsmtIndexDO::getSort, asmtIndexDO.getSort()));
        }
    }

    @Override
    public AsmtModelDO getAsmtModelByPkgId(Long pkgId) {
        return asmtModelMapper.getByPkgId(pkgId);
    }

    @Override
    public List<AsmtIndexDO> getIndexList(Long modelId) {
        AsmtIndexListReqVO reqVO = new AsmtIndexListReqVO();
        reqVO.setModelId(modelId);
        reqVO.setStatus(0);
        return asmtIndexMapper.getList(reqVO);
    }

    @Override
    public List<AsmtIndexDO> getCalcIndexList(Long modelId) {
        return asmtIndexMapper.selectList(new LambdaUpdateWrapper<AsmtIndexDO>()
                .eq(AsmtIndexDO::getModelId, modelId)
                .eq(AsmtIndexDO::getIsImport, 0)
                .orderByAsc(AsmtIndexDO::getSort));
    }

    private void validateAsmtIndexExists(Long id) {
        if (asmtIndexMapper.selectById(id) == null) {
            throw exception(ASMT_INDEX_NOT_EXISTS);
        }
    }

    private void deleteAsmtIndexByModelId(Long modelId) {
        asmtIndexMapper.deleteByModelId(modelId);
    }

}
