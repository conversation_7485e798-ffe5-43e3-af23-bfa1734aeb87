package cn.iocoder.yudao.module.hrm.dal.dataobject.stdscore;

import lombok.*;
import com.baomidou.mybatisplus.annotation.*;
import cn.iocoder.yudao.framework.mybatis.core.dataobject.BaseDO;

/**
 * 指标原始分转标准分区间设置 DO
 *
 * <AUTHOR>
 */
@TableName("hrm_asmt_index_stdscore")
@KeySequence("hrm_asmt_index_stdscore_seq") // 用于 Oracle、PostgreSQL、Kingbase、DB2、H2 数据库的主键自增。如果是 MySQL 等数据库，可不写。
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class AsmtIndexStdscoreDO extends BaseDO {

    /**
     * id
     */
    @TableId
    private Long id;
    /**
     * 指标编码
     */
    private String indexCode;
    /**
     * 原始分下区间
     */
    private Integer rawMin;
    /**
     * 原始分上区间
     */
    private Integer rawMax;
    /**
     * 对应标准分
     */
    private Integer stdScore;

}