package cn.iocoder.yudao.module.hrm.controller.admin.paper.vo;

import cn.iocoder.yudao.module.hrm.controller.admin.question.vo.AsmtQuestionOptionRespVO;
import com.alibaba.excel.annotation.ExcelIgnoreUnannotated;
import com.alibaba.excel.annotation.ExcelProperty;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.time.LocalDateTime;
import java.util.List;

@Schema(description = "管理后台 - 测评卷与测评题关联 Response VO")
@Data
@ExcelIgnoreUnannotated
public class AsmtPaperQuestionRespVO {

    @Schema(description = "标签id", requiredMode = Schema.RequiredMode.REQUIRED, example = "29593")
    @ExcelProperty("标签id")
    private Long id;

    @Schema(description = "创建时间")
    @ExcelProperty("创建时间")
    private LocalDateTime createTime;

    @Schema(description = "试卷", requiredMode = Schema.RequiredMode.REQUIRED, example = "32105")
    @ExcelProperty("试卷")
    private Long paperId;

    @Schema(description = "测评题id", requiredMode = Schema.RequiredMode.REQUIRED, example = "23009")
    @ExcelProperty("测评题id")
    private Long quesId;

    @Schema(description = "测评题")
    @ExcelProperty("测评题")
    private String quesTitle;

    @Schema(description = "显示顺序")
    @ExcelProperty("显示顺序")
    private Integer sort;

    private List<AsmtQuestionOptionRespVO> optionList;
}
