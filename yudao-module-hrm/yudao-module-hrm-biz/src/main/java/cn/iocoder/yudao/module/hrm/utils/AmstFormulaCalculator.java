package cn.iocoder.yudao.module.hrm.utils;

import org.apache.commons.lang3.StringUtils;
import org.apache.commons.math3.util.Precision;

import java.util.*;
import java.util.regex.Matcher;
import java.util.regex.Pattern;

/**
 * 公式计算工具类
 * 支持基础运算、条件函数、数学函数、逻辑函数等
 *
 * <AUTHOR>
 */
public class AmstFormulaCalculator {
    private static final Pattern FUNCTION_PATTERN = Pattern.compile("([A-Z]+)\\((.*)\\)");
    private static final Pattern VARIABLE_PATTERN = Pattern.compile("\\$\\{([a-zA-Z0-9.]+)}");
    private static final Pattern NUMBER_PATTERN = Pattern.compile("^[-+]?[0-9]*\\.?[0-9]+([eE][-+]?[0-9]+)?$");
    private final Map<String, Double> variables = new HashMap<>();

    // 支持的运算符
    private static final List<String> SUPPORTED_OPERATORS = Arrays.asList("+", "-", "*", "/", "^", ">=", "<=", "==", "!=", ">", "<");

    /**
     * 自定义异常类，用于公式计算过程中的异常处理
     */
    public static class FormulaEvaluationException extends RuntimeException {
        private static final long serialVersionUID = 1L;

        public FormulaEvaluationException(String message) {
            super(message);
        }

        public FormulaEvaluationException(String message, Throwable cause) {
            super(message, cause);
        }
    }

    /**
     * 计算公式
     *
     * @param formula 公式字符串
     * @param variables 变量Map，可以为null或空
     * @return 计算结果
     * @throws FormulaEvaluationException 当公式无效或计算过程中出现错误时抛出
     */
    public static double calculate(String formula, Map<String, Double> variables) {
        if (StringUtils.isBlank(formula)) {
            throw new FormulaEvaluationException("空公式无效");
        }

        AmstFormulaCalculator calculator = new AmstFormulaCalculator();
        if (variables != null) {
            calculator.setVariables(variables);
        }

        try {
            return calculator.evaluateExpression(formula.trim());
        } catch (RuntimeException e) {
            throw e; // 直接传递自定义异常
        }
    }

    /**
     * 计算没有变量的公式
     *
     * @param formula 公式字符串
     * @return 计算结果
     * @throws FormulaEvaluationException 当公式无效或计算过程中出现错误时抛出
     */
    public static double calculate(String formula) {
        return calculate(formula, null);
    }

    /**
     * 设置变量值
     *
     * @param name  变量名
     * @param value 变量值
     * @throws IllegalArgumentException 如果变量名不符合规则（只允许字母、数字和点）
     */
    public void setVariable(String name, double value) {
        validateVariableName(name);
        variables.put(name, value);
    }

    /**
     * 设置多个变量值
     *
     * @param variableMap 变量Map
     * @throws IllegalArgumentException 如果有变量名不符合规则（只允许字母、数字和点）
     */
    public void setVariables(Map<String, Double> variableMap) {
        for (String name : variableMap.keySet()) {
            validateVariableName(name);
        }
        variables.putAll(variableMap);
    }

    /**
     * 验证变量名是否符合规则（只允许字母、数字和点）
     *
     * @param name 变量名
     * @throws IllegalArgumentException 如果变量名不符合规则
     */
    private void validateVariableName(String name) {
        if (!name.matches("[a-zA-Z0-9.]+")) {
            throw new IllegalArgumentException("变量名只能包含字母、数字和点，不符合规则的变量名: " + name);
        }
    }

    /**
     * 验证公式是否有效
     * 优化后的方法更好地处理带空格的公式
     *
     * @param formula 公式字符串
     * @return 如果公式有效返回true，否则返回false
     */
    public static boolean validateFormula(String formula) {
        if (StringUtils.isBlank(formula)) {
            return false; // 空公式视为无效
        }

        try {
            // 检查变量名是否符合规则
            Matcher matcher = VARIABLE_PATTERN.matcher(formula);
            while (matcher.find()) {
                String varName = matcher.group(1);
                if (!varName.matches("[a-zA-Z0-9.]+")) {
                    return false; // 变量名不符合规则
                }
            }

            // 使用一个空的变量Map来验证公式结构
            // 注意：这里不需要额外调用trim()，因为evaluateExpression内部的tokenizeExpression会处理空格
            new AmstFormulaCalculator().evaluateExpression(formula.replaceAll(VARIABLE_PATTERN.pattern(), "1"));
            return true;
        } catch (Exception e) {
            return false;
        }
    }

    /**
     * 验证公式是否有效，如果无效则抛出异常
     * 优化后的方法更好地处理带空格的公式
     *
     * @param formula 公式字符串
     * @throws FormulaEvaluationException 当公式无效时抛出
     */
    public static void validateFormulaWithException(String formula) {
        if (StringUtils.isBlank(formula)) {
            throw new FormulaEvaluationException("空公式无效"); // 空公式视为无效并抛出异常
        }

        try {
            // 检查变量名是否符合规则
            Matcher matcher = VARIABLE_PATTERN.matcher(formula);
            while (matcher.find()) {
                String varName = matcher.group(1);
                if (!varName.matches("[a-zA-Z0-9.]+")) {
                    throw new FormulaEvaluationException("变量名不符合规则: " + varName);
                }
            }

            // 使用一个空的变量Map来验证公式结构
            // 注意：这里不需要额外调用trim()，因为evaluateExpression内部的tokenizeExpression会处理空格
            new AmstFormulaCalculator().evaluateExpression(formula.replaceAll(VARIABLE_PATTERN.pattern(), "1"));
        } catch (RuntimeException e) {
            throw new FormulaEvaluationException("公式验证失败: " + e.getMessage(), e);
        }
    }

    private double evaluateExpression(String expression) {
        // 处理函数
        Matcher functionMatcher = FUNCTION_PATTERN.matcher(expression);
        if (functionMatcher.find()) {
            String functionName = functionMatcher.group(1);
            String argsStr = functionMatcher.group(2);
            String[] args = splitFunctionArgs(argsStr);
            return evaluateFunction(functionName, args);
        }

        // 处理变量
        Matcher variableMatcher = VARIABLE_PATTERN.matcher(expression);
        if (variableMatcher.find()) {
            // 替换所有变量
            String processedExpression = replaceVariables(expression);
            return evaluateBasicExpression(processedExpression);
        }

        // 处理基础运算
        return evaluateBasicExpression(expression);
    }

    private String replaceVariables(String expression) {
        Matcher matcher = VARIABLE_PATTERN.matcher(expression);
        StringBuffer result = new StringBuffer();

        while (matcher.find()) {
            String varName = matcher.group(1);
            Double value = variables.get(varName);
            if (value == null) {
                throw new FormulaEvaluationException("未赋值的变量: " + varName);
            }
            matcher.appendReplacement(result, String.valueOf(value));
        }

        matcher.appendTail(result);
        return result.toString();
    }

    private String[] splitFunctionArgs(String argsStr) {
        List<String> args = new ArrayList<>();
        int start = 0;
        int parenthesesCount = 0;

        for (int i = 0; i < argsStr.length(); i++) {
            char c = argsStr.charAt(i);
            if (c == '(') {
                parenthesesCount++;
            } else if (c == ')') {
                parenthesesCount--;
            } else if (c == ',' && parenthesesCount == 0) {
                args.add(argsStr.substring(start, i).trim());
                start = i + 1;
            }
        }

        // 添加最后一个参数
        if (start < argsStr.length()) {
            args.add(argsStr.substring(start).trim());
        }

        return args.toArray(new String[0]);
    }

    private double evaluateFunction(String functionName, String[] args) {
        double[] values = Arrays.stream(args)
                .map(String::trim)
                .mapToDouble(this::evaluateExpression)
                .toArray();

        switch (functionName.toUpperCase()) {
            case "IF":
                if (args.length != 3) {
                    throw new FormulaEvaluationException("IF函数需要3个参数");
                }
                return values[0] != 0 ? values[1] : values[2];
            case "SUM":
                return Arrays.stream(values).sum();
            case "AVERAGE":
                return Arrays.stream(values).average().orElse(0.0);
            case "MAX":
                return Arrays.stream(values).max().orElse(0.0);
            case "MIN":
                return Arrays.stream(values).min().orElse(0.0);
            case "AND":
                return Arrays.stream(values).allMatch(v -> v != 0) ? 1.0 : 0.0;
            case "OR":
                return Arrays.stream(values).anyMatch(v -> v != 0) ? 1.0 : 0.0;
            case "NOT":
                if (args.length != 1) {
                    throw new FormulaEvaluationException("NOT函数需要1个参数");
                }
                return values[0] == 0 ? 1.0 : 0.0;
            default:
                throw new FormulaEvaluationException("不支持的函数: " + functionName);
        }
    }

    private double evaluateBasicExpression(String expression) {
        // 处理括号
        int start = expression.indexOf('(');
        if (start != -1) {
            int end = findMatchingParenthesis(expression, start);
            String subExpression = expression.substring(start + 1, end);
            double result = evaluateBasicExpression(subExpression);
            return evaluateBasicExpression(
                    expression.substring(0, start) + result + expression.substring(end + 1));
        }

        // 直接返回数字
        if (isNumeric(expression)) {
            return Double.parseDouble(expression);
        }

        // 处理比较运算符
        for (String operator : Arrays.asList(">=", "<=", "==", "!=", ">", "<")) {
            if (expression.contains(operator)) {
                String[] parts = splitByOperator(expression, operator);
                if (parts.length != 2) {
                    throw new FormulaEvaluationException("无效的表达式: " + expression + "，运算符 " + operator + " 使用错误");
                }
                double left = evaluateBasicExpression(parts[0].trim());
                double right = evaluateBasicExpression(parts[1].trim());

                switch (operator) {
                    case ">=":
                        return left >= right ? 1.0 : 0.0;
                    case "<=":
                        return left <= right ? 1.0 : 0.0;
                    case "==":
                        return Math.abs(left - right) < 1e-10 ? 1.0 : 0.0;
                    case "!=":
                        return Math.abs(left - right) >= 1e-10 ? 1.0 : 0.0;
                    case ">":
                        return left > right ? 1.0 : 0.0;
                    case "<":
                        return left < right ? 1.0 : 0.0;
                    default:
                        throw new FormulaEvaluationException("不支持的比较运算符: " + operator);
                }
            }
        }

        // 解析表达式中的标记和运算符
        List<String> tokens = tokenizeExpression(expression);

        if (tokens.isEmpty()) {
            throw new FormulaEvaluationException("无效的表达式: " + expression);
        }

        if (tokens.size() == 1) {
            if (isNumeric(tokens.get(0))) {
                return Double.parseDouble(tokens.get(0));
            } else {
                throw new FormulaEvaluationException("无效的表达式: " + expression);
            }
        }

        // 检查表达式语法是否正确
        validateExpressionSyntax(tokens);

        // 处理乘除和幂运算
        for (int i = 1; i < tokens.size() - 1; i += 2) {
            if (tokens.get(i).equals("*") || tokens.get(i).equals("/") || tokens.get(i).equals("^")) {
                if (!isNumeric(tokens.get(i - 1)) || !isNumeric(tokens.get(i + 1))) {
                    throw new FormulaEvaluationException("无效的表达式: " + expression + "，运算符 " + tokens.get(i) + " 两边必须是数字");
                }

                double left = Double.parseDouble(tokens.get(i - 1));
                double right = Double.parseDouble(tokens.get(i + 1));
                double result;

                switch (tokens.get(i)) {
                    case "*":
                        result = left * right;
                        break;
                    case "/":
                        if (right == 0) {
                            throw new ArithmeticException("除数不能为零");
                        }
                        result = left / right;
                        break;
                    case "^":
                        result = Math.pow(left, right);
                        break;
                    default:
                        throw new FormulaEvaluationException("不支持的运算符: " + tokens.get(i));
                }

                tokens.set(i - 1, String.valueOf(result));
                tokens.remove(i);
                tokens.remove(i);
                i -= 2;
            }
        }

        // 处理加减运算
        if (tokens.isEmpty()) {
            throw new FormulaEvaluationException("无效的表达式: " + expression);
        }

        if (!isNumeric(tokens.get(0))) {
            throw new FormulaEvaluationException("无效的表达式: " + expression + "，表达式必须以数字开始");
        }

        double result = Double.parseDouble(tokens.get(0));
        for (int i = 1; i < tokens.size(); i += 2) {
            if (i + 1 >= tokens.size()) {
                throw new FormulaEvaluationException("无效的表达式: " + expression + "，运算符 " + tokens.get(i) + " 后必须有操作数");
            }

            if (!isNumeric(tokens.get(i + 1))) {
                throw new FormulaEvaluationException("无效的表达式: " + expression + "，运算符 " + tokens.get(i) + " 后必须是数字");
            }

            double right = Double.parseDouble(tokens.get(i + 1));
            switch (tokens.get(i)) {
                case "+":
                    result += right;
                    break;
                case "-":
                    result -= right;
                    break;
                default:
                    throw new FormulaEvaluationException("不支持的运算符: " + tokens.get(i));
            }
        }

        return Precision.round(result, 10);
    }

    private boolean isNumeric(String str) {
        if (str == null) {
            return false;
        }
        return NUMBER_PATTERN.matcher(str).matches();
    }

    private String[] splitByOperator(String expression, String operator) {
        List<String> parts = new ArrayList<>();
        int pos = expression.indexOf(operator);
        if (pos == -1) {
            return new String[]{expression};
        }

        // 检查是否是真正的运算符，而不是变量名的一部分等
        parts.add(expression.substring(0, pos));
        parts.add(expression.substring(pos + operator.length()));

        return parts.toArray(new String[0]);
    }

    /**
     * 将表达式分解为标记（操作数和运算符）
     * 优化后的方法更好地处理空格，允许公式中包含任意空格
     *
     * @param expression 要分解的表达式
     * @return 标记列表
     */
    private List<String> tokenizeExpression(String expression) {
        List<String> tokens = new ArrayList<>();
        StringBuilder currentToken = new StringBuilder();

        // 预处理：移除表达式中所有空格，简化后续处理
        String preprocessedExpression = expression.replaceAll("\\s+", "");

        for (int i = 0; i < preprocessedExpression.length(); i++) {
            char c = preprocessedExpression.charAt(i);

            if (c == '+' || c == '-' || c == '*' || c == '/' || c == '^') {
                // 如果当前有累积的标记，先添加到结果中
                if (currentToken.length() > 0) {
                    tokens.add(currentToken.toString());
                    currentToken = new StringBuilder();
                }
                // 添加运算符作为单独的标记
                tokens.add(String.valueOf(c));
            } else {
                // 累积当前字符到标记中
                currentToken.append(c);
            }
        }

        // 添加最后一个标记（如果有）
        if (currentToken.length() > 0) {
            tokens.add(currentToken.toString());
        }

        return tokens;
    }

    private void validateExpressionSyntax(List<String> tokens) {
        // 表达式必须至少有一个操作数
        if (tokens.isEmpty()) {
            throw new FormulaEvaluationException("表达式不能为空");
        }

        // 表达式必须以操作数开始和结束
        if (!isNumeric(tokens.get(0))) {
            throw new FormulaEvaluationException("表达式必须以数字开始，而不是: " + tokens.get(0));
        }

        if (!isNumeric(tokens.get(tokens.size() - 1))) {
            throw new FormulaEvaluationException("表达式必须以数字结束，而不是: " + tokens.get(tokens.size() - 1));
        }

        // 检查运算符和操作数交替出现
        for (int i = 0; i < tokens.size(); i++) {
            if (i % 2 == 0) {
                // 偶数位置应该是操作数
                if (!isNumeric(tokens.get(i))) {
                    throw new FormulaEvaluationException("表达式中数字和运算符必须交替出现，在位置 " + i + " 期望数字，但得到: " + tokens.get(i));
                }
            } else {
                // 奇数位置应该是运算符
                String operator = tokens.get(i);
                if (!SUPPORTED_OPERATORS.contains(operator)) {
                    throw new FormulaEvaluationException("不支持的运算符: " + operator);
                }
            }
        }
    }

    private int findMatchingParenthesis(String expression, int start) {
        int count = 1;
        for (int i = start + 1; i < expression.length(); i++) {
            if (expression.charAt(i) == '(') {
                count++;
            } else if (expression.charAt(i) == ')') {
                count--;
                if (count == 0) {
                    return i;
                }
            }
        }
        throw new FormulaEvaluationException("括号不匹配");
    }
}