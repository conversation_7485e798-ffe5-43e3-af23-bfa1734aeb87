package cn.iocoder.yudao.module.hrm.service.packages;

import cn.iocoder.yudao.framework.common.pojo.PageResult;
import cn.iocoder.yudao.module.hrm.controller.admin.packages.vo.AsmtPackagePageReqVO;
import cn.iocoder.yudao.module.hrm.controller.admin.packages.vo.AsmtPackageRespVO;
import cn.iocoder.yudao.module.hrm.controller.admin.packages.vo.AsmtPackageSaveReqVO;
import cn.iocoder.yudao.module.hrm.dal.dataobject.packages.AsmtPackageDO;
import jakarta.validation.Valid;

/**
 * 测评包 Service 接口
 *
 * <AUTHOR>
 */
public interface AsmtPackageService {

    /**
     * 创建测评包
     *
     * @param createReqVO 创建信息
     * @return 编号
     */
    Long createAsmtPackage(@Valid AsmtPackageSaveReqVO createReqVO);

    /**
     * 更新测评包
     *
     * @param updateReqVO 更新信息
     */
    void updateAsmtPackage(@Valid AsmtPackageSaveReqVO updateReqVO);

    /**
     * 删除测评包
     *
     * @param id 编号
     */
    void deleteAsmtPackage(Long id);

    /**
     * 获得测评包
     *
     * @param id 编号
     * @return 测评包
     */
    AsmtPackageDO getAsmtPackage(Long id);

    /**
     * 获得测评包分页
     *
     * @param pageReqVO 分页查询
     * @return 测评包分页
     */
    PageResult<AsmtPackageDO> getAsmtPackagePage(AsmtPackagePageReqVO pageReqVO);
    PageResult<AsmtPackageRespVO> getAsmtPackagePageWithPaper(AsmtPackagePageReqVO pageReqVO);

}
