package cn.iocoder.yudao.module.hrm.service.paper;

import cn.iocoder.yudao.framework.common.pojo.PageResult;
import cn.iocoder.yudao.module.hrm.controller.admin.paper.vo.AsmtPaperPageReqVO;
import cn.iocoder.yudao.module.hrm.controller.admin.paper.vo.AsmtPaperSaveReqVO;
import cn.iocoder.yudao.module.hrm.dal.dataobject.paper.AsmtPaperDO;
import jakarta.validation.Valid;

/**
 * 测评卷 Service 接口
 *
 * <AUTHOR>
 */
public interface AsmtPaperService {

    /**
     * 创建测评卷
     *
     * @param createReqVO 创建信息
     * @return 编号
     */
    Long createAsmtPaper(@Valid AsmtPaperSaveReqVO createReqVO);

    /**
     * 更新测评卷
     *
     * @param updateReqVO 更新信息
     */
    void updateAsmtPaper(@Valid AsmtPaperSaveReqVO updateReqVO);

    /**
     * 删除测评卷
     *
     * @param id 编号
     */
    void deleteAsmtPaper(Long id);

    /**
     * 获得测评卷
     *
     * @param id 编号
     * @return 测评卷
     */
    AsmtPaperDO getAsmtPaper(Long id);

    /**
     * 获得测评卷分页
     *
     * @param pageReqVO 分页查询
     * @return 测评卷分页
     */
    PageResult<AsmtPaperDO> getAsmtPaperPage(AsmtPaperPageReqVO pageReqVO);

}