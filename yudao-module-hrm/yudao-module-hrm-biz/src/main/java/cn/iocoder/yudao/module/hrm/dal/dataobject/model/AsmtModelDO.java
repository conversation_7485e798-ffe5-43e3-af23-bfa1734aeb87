package cn.iocoder.yudao.module.hrm.dal.dataobject.model;

import cn.iocoder.yudao.framework.mybatis.core.dataobject.BaseDO;
import com.baomidou.mybatisplus.annotation.KeySequence;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.*;

/**
 * 测评模型 DO
 *
 * <AUTHOR>
 */
@TableName("hrm_asmt_model")
@KeySequence("hrm_asmt_model_seq") // 用于 Oracle、PostgreSQL、Kingbase、DB2、H2 数据库的主键自增。如果是 MySQL 等数据库，可不写。
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class AsmtModelDO extends BaseDO {

    /**
     * 标签id
     */
    @TableId
    private Long id;
    /**
     * 模型名称
     */
    private String modelName;
    /**
     * 测评包id
     */
    private Long pkgId;
    /**
     * 说明
     */
    private String description;
    /**
     * 否决项指标id
     */
    private Long disIndex;
    /**
     * 状态
     */
    private Integer status;

    /**
     * 测评报告模板
     */
    private String tempCode;

}
