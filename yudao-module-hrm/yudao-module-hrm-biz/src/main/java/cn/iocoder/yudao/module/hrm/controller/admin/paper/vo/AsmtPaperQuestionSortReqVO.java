package cn.iocoder.yudao.module.hrm.controller.admin.paper.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotNull;
import lombok.Data;

@Schema(description = "管理后台 - 测评卷与测评题关联修改排序 Request VO")
@Data
public class AsmtPaperQuestionSortReqVO {

    @Schema(description = "id")
    @NotNull(message = "id不能为空")
    private Long id;

    @Schema(description = "显示顺序")
    @NotNull(message = "显示顺序不能为空")
    private Integer sort;

}
