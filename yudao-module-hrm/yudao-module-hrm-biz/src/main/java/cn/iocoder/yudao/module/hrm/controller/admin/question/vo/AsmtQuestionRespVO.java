package cn.iocoder.yudao.module.hrm.controller.admin.question.vo;

import cn.iocoder.yudao.framework.excel.core.annotations.DictFormat;
import cn.iocoder.yudao.framework.excel.core.convert.DictConvert;
import com.alibaba.excel.annotation.ExcelIgnoreUnannotated;
import com.alibaba.excel.annotation.ExcelProperty;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.time.LocalDateTime;

@Schema(description = "管理后台 - 测评题 Response VO")
@Data
@ExcelIgnoreUnannotated
public class AsmtQuestionRespVO {

    @Schema(description = "标签id", requiredMode = Schema.RequiredMode.REQUIRED, example = "18731")
    @ExcelProperty("标签id")
    private Long id;

    @Schema(description = "状态", example = "2")
    @ExcelProperty(value = "状态", converter = DictConvert.class)
    @DictFormat("common_status") // TODO 代码优化：建议设置到对应的 DictTypeConstants 枚举类中
    private Integer status;

    @Schema(description = "题目编码")
    @ExcelProperty("题目编码")
    private String quseCode;

    @Schema(description = "测评类型", example = "1")
    @ExcelProperty(value = "测评类型", converter = DictConvert.class)
    @DictFormat("asmt_type") // TODO 代码优化：建议设置到对应的 DictTypeConstants 枚举类中
    private String asmtType;

    @Schema(description = "题型", example = "1")
    @ExcelProperty("题型")
    private String quesType;

    @Schema(description = "题目", requiredMode = Schema.RequiredMode.REQUIRED)
    @ExcelProperty("题目")
    private String title;

    @Schema(description = "题目说明", example = "你猜")
    @ExcelProperty("题目说明")
    private String description;

    @Schema(description = "标签")
    @ExcelProperty("标签")
    private String tags;

    @Schema(description = "创建时间", requiredMode = Schema.RequiredMode.REQUIRED)
    @ExcelProperty("创建时间")
    private LocalDateTime createTime;
}