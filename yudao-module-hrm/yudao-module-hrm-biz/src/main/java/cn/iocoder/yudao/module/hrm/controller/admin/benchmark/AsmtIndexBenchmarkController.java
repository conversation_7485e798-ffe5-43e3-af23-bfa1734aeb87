package cn.iocoder.yudao.module.hrm.controller.admin.benchmark;

import org.springframework.web.bind.annotation.*;
import jakarta.annotation.Resource;
import org.springframework.validation.annotation.Validated;
import org.springframework.security.access.prepost.PreAuthorize;
import io.swagger.v3.oas.annotations.tags.Tag;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.Operation;

import jakarta.validation.*;
import jakarta.servlet.http.*;

import java.util.*;
import java.io.IOException;

import cn.iocoder.yudao.framework.common.pojo.PageParam;
import cn.iocoder.yudao.framework.common.pojo.PageResult;
import cn.iocoder.yudao.framework.common.pojo.CommonResult;
import cn.iocoder.yudao.framework.common.util.object.BeanUtils;

import static cn.iocoder.yudao.framework.common.pojo.CommonResult.success;

import cn.iocoder.yudao.framework.excel.core.util.ExcelUtils;

import cn.iocoder.yudao.framework.apilog.core.annotation.ApiAccessLog;

import static cn.iocoder.yudao.framework.apilog.core.enums.OperateTypeEnum.*;

import cn.iocoder.yudao.module.hrm.controller.admin.benchmark.vo.*;
import cn.iocoder.yudao.module.hrm.dal.dataobject.benchmark.AsmtIndexBenchmarkDO;
import cn.iocoder.yudao.module.hrm.service.benchmark.AsmtIndexBenchmarkService;

@Tag(name = "管理后台 - 序列岗位指标基准")
@RestController
@RequestMapping("/hrm/asmt-index-benchmark")
@Validated
public class AsmtIndexBenchmarkController {

    @Resource
    private AsmtIndexBenchmarkService asmtIndexBenchmarkService;

    @PostMapping("/create")
    @Operation(summary = "创建序列岗位指标基准")
    @PreAuthorize("@ss.hasPermission('hrm:asmt-index-benchmark:create')")
    public CommonResult<Long> createAsmtIndexBenchmark(@Valid @RequestBody AsmtIndexBenchmarkSaveReqVO createReqVO) {
        return success(asmtIndexBenchmarkService.createAsmtIndexBenchmark(createReqVO));
    }

    @PutMapping("/update")
    @Operation(summary = "更新序列岗位指标基准")
    @PreAuthorize("@ss.hasPermission('hrm:asmt-index-benchmark:update')")
    public CommonResult<Boolean> updateAsmtIndexBenchmark(@Valid @RequestBody AsmtIndexBenchmarkSaveReqVO updateReqVO) {
        asmtIndexBenchmarkService.updateAsmtIndexBenchmark(updateReqVO);
        return success(true);
    }

    @DeleteMapping("/delete")
    @Operation(summary = "删除序列岗位指标基准")
    @Parameter(name = "id", description = "编号", required = true)
    @PreAuthorize("@ss.hasPermission('hrm:asmt-index-benchmark:delete')")
    public CommonResult<Boolean> deleteAsmtIndexBenchmark(@RequestParam("id") Long id) {
        asmtIndexBenchmarkService.deleteAsmtIndexBenchmark(id);
        return success(true);
    }

    @GetMapping("/get")
    @Operation(summary = "获得序列岗位指标基准")
    @Parameter(name = "id", description = "编号", required = true, example = "1024")
    @PreAuthorize("@ss.hasPermission('hrm:asmt-index-benchmark:query')")
    public CommonResult<AsmtIndexBenchmarkRespVO> getAsmtIndexBenchmark(@RequestParam("id") Long id) {
        AsmtIndexBenchmarkDO asmtIndexBenchmark = asmtIndexBenchmarkService.getAsmtIndexBenchmark(id);
        return success(BeanUtils.toBean(asmtIndexBenchmark, AsmtIndexBenchmarkRespVO.class));
    }

    @GetMapping("/get-by-series-level")
    @Operation(summary = "获得序列岗位指标基准")
    @Parameter(name = "series", description = "序列", required = true, example = "管理序列")
    @Parameter(name = "level", description = "职级", required = true, example = "职级-5")
    public CommonResult<AsmtIndexBenchmarkRespVO> getIndexBenchmarkBySeriesLevel(@RequestParam("series") String series,
                                                                                 @RequestParam("level") String level) {
        AsmtIndexBenchmarkDO asmtIndexBenchmark = asmtIndexBenchmarkService.getIndexBenchmarkBySeriesLevel(series, level);
        return success(BeanUtils.toBean(asmtIndexBenchmark, AsmtIndexBenchmarkRespVO.class));
    }

    @GetMapping("/page")
    @Operation(summary = "获得序列岗位指标基准分页")
    @PreAuthorize("@ss.hasPermission('hrm:asmt-index-benchmark:query')")
    public CommonResult<PageResult<AsmtIndexBenchmarkRespVO>> getAsmtIndexBenchmarkPage(@Valid AsmtIndexBenchmarkPageReqVO pageReqVO) {
        PageResult<AsmtIndexBenchmarkDO> pageResult = asmtIndexBenchmarkService.getAsmtIndexBenchmarkPage(pageReqVO);
        return success(BeanUtils.toBean(pageResult, AsmtIndexBenchmarkRespVO.class));
    }

    @GetMapping("/export-excel")
    @Operation(summary = "导出序列岗位指标基准 Excel")
    @PreAuthorize("@ss.hasPermission('hrm:asmt-index-benchmark:export')")
    @ApiAccessLog(operateType = EXPORT)
    public void exportAsmtIndexBenchmarkExcel(@Valid AsmtIndexBenchmarkPageReqVO pageReqVO,
                                              HttpServletResponse response) throws IOException {
        pageReqVO.setPageSize(PageParam.PAGE_SIZE_NONE);
        List<AsmtIndexBenchmarkDO> list = asmtIndexBenchmarkService.getAsmtIndexBenchmarkPage(pageReqVO).getList();
        // 导出 Excel
        ExcelUtils.write(response, "序列岗位指标基准.xls", "数据", AsmtIndexBenchmarkRespVO.class,
                BeanUtils.toBean(list, AsmtIndexBenchmarkRespVO.class));
    }

}
