package cn.iocoder.yudao.module.hrm.service.benchmark;

import jakarta.validation.*;
import cn.iocoder.yudao.module.hrm.controller.admin.benchmark.vo.*;
import cn.iocoder.yudao.module.hrm.dal.dataobject.benchmark.AsmtIndexBenchmarkDO;
import cn.iocoder.yudao.framework.common.pojo.PageResult;

/**
 * 序列岗位指标基准 Service 接口
 *
 * <AUTHOR>
 */
public interface AsmtIndexBenchmarkService {

    /**
     * 创建序列岗位指标基准
     *
     * @param createReqVO 创建信息
     * @return 编号
     */
    Long createAsmtIndexBenchmark(@Valid AsmtIndexBenchmarkSaveReqVO createReqVO);

    /**
     * 更新序列岗位指标基准
     *
     * @param updateReqVO 更新信息
     */
    void updateAsmtIndexBenchmark(@Valid AsmtIndexBenchmarkSaveReqVO updateReqVO);

    /**
     * 删除序列岗位指标基准
     *
     * @param id 编号
     */
    void deleteAsmtIndexBenchmark(Long id);

    /**
     * 获得序列岗位指标基准
     *
     * @param id 编号
     * @return 序列岗位指标基准
     */
    AsmtIndexBenchmarkDO getAsmtIndexBenchmark(Long id);

    AsmtIndexBenchmarkDO getIndexBenchmarkBySeriesLevel(String series, String level);

    /**
     * 获得序列岗位指标基准分页
     *
     * @param pageReqVO 分页查询
     * @return 序列岗位指标基准分页
     */
    PageResult<AsmtIndexBenchmarkDO> getAsmtIndexBenchmarkPage(AsmtIndexBenchmarkPageReqVO pageReqVO);

}
