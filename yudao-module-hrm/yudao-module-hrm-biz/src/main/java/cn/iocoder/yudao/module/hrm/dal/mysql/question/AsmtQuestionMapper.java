package cn.iocoder.yudao.module.hrm.dal.mysql.question;

import cn.iocoder.yudao.framework.common.pojo.PageResult;
import cn.iocoder.yudao.framework.mybatis.core.mapper.SuperMapperX;
import cn.iocoder.yudao.framework.mybatis.core.query.LambdaQueryWrapperX;
import cn.iocoder.yudao.module.hrm.controller.admin.question.vo.AsmtQuestionPageReqVO;
import cn.iocoder.yudao.module.hrm.dal.dataobject.question.AsmtQuestionDO;
import org.apache.ibatis.annotations.Mapper;

/**
 * 测评题 Mapper
 *
 * <AUTHOR>
 */
@Mapper
public interface AsmtQuestionMapper extends SuperMapperX<AsmtQuestionDO> {

    default PageResult<AsmtQuestionDO> selectPage(AsmtQuestionPageReqVO reqVO) {
        LambdaQueryWrapperX<AsmtQuestionDO> query = new LambdaQueryWrapperX<AsmtQuestionDO>()
                .eqIfPresent(AsmtQuestionDO::getStatus, reqVO.getStatus())
                .eqIfPresent(AsmtQuestionDO::getQuseCode, reqVO.getQuseCode())
                .eqIfPresent(AsmtQuestionDO::getAsmtType, reqVO.getAsmtType())
                .likeIfPresent(AsmtQuestionDO::getTags, reqVO.getTags())
                .likeIfPresent(AsmtQuestionDO::getTitle, reqVO.getTitle());

        // 如果没有排序字段，则默认按照ID倒序排序
        if (reqVO.getSortingFields() == null || reqVO.getSortingFields().isEmpty()) {
            query.orderByDesc(AsmtQuestionDO::getId);
        }

        return selectPage(reqVO, query);
    }

}