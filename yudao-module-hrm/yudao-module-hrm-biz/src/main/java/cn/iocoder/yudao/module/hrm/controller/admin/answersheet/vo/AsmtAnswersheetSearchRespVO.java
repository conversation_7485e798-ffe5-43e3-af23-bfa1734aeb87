package cn.iocoder.yudao.module.hrm.controller.admin.answersheet.vo;

import com.alibaba.excel.annotation.ExcelIgnoreUnannotated;
import com.alibaba.excel.annotation.ExcelProperty;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.time.LocalDateTime;

@Schema(description = "管理后台 - 用户答题卡 Response VO")
@Data
@ExcelIgnoreUnannotated
public class AsmtAnswersheetSearchRespVO {

    @ExcelProperty("id，没什么用的")
    private String id;

    @Schema(description = "用户id", requiredMode = Schema.RequiredMode.REQUIRED, example = "17769")
    @ExcelProperty("用户id")
    private Long userId;

    @Schema(description = "测评任务id", example = "20312")
    @ExcelProperty("测评任务id")
    private Long taskId;

    @ExcelProperty("测评任务与用户关联表的ID")
    private Long userTaskId;

    private LocalDateTime createTime;

    @Schema(description = "测试包id", example = "17698")
    @ExcelProperty("测试包id")
    private Long pkgId;

    @Schema(description = "测试卷id", example = "29248")
    @ExcelProperty("测试卷id")
    private Long paperId;

    private String taskName;
    private Long taskStatus;
    private String nickname;
    private String paperName;
    private Integer minutes;
    private String asmtType;
    private Integer paperStatus;

}
