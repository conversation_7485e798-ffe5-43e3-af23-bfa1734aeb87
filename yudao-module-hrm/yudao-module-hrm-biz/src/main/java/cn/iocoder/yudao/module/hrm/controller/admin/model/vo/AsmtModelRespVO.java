package cn.iocoder.yudao.module.hrm.controller.admin.model.vo;

import com.alibaba.excel.annotation.ExcelIgnoreUnannotated;
import com.alibaba.excel.annotation.ExcelProperty;
import com.fhs.core.trans.anno.Trans;
import com.fhs.core.trans.constant.TransType;
import com.fhs.core.trans.vo.VO;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.time.LocalDateTime;

@Schema(description = "管理后台 - 测评模型 Response VO")
@Data
@ExcelIgnoreUnannotated
public class AsmtModelRespVO implements VO {

    @Schema(description = "标签id", requiredMode = Schema.RequiredMode.REQUIRED, example = "23937")
    @ExcelProperty("标签id")
    private Long id;

    @Schema(description = "模型名称", requiredMode = Schema.RequiredMode.REQUIRED, example = "赵六")
    @ExcelProperty("模型名称")
    private String modelName;

    @Schema(description = "测评包id", requiredMode = Schema.RequiredMode.REQUIRED, example = "24722")
    @ExcelProperty("测评包id")
    @Trans(type = TransType.SIMPLE,
            fields = "pkgName",
            targetClassName = "cn.iocoder.yudao.module.hrm.dal.dataobject.packages.AsmtPackageDO",
            ref = "pkgName"
    )
    private Long pkgId;

    @Schema(description = "测评包名称")
    @ExcelProperty("测评包名称")
    private String pkgName;

    @Schema(description = "说明", example = "随便")
    @ExcelProperty("说明")
    private String description;

    @Schema(description = "创建时间")
    @ExcelProperty("创建时间")
    private LocalDateTime createTime;

    @Schema(description = "状态", example = "1")
    @ExcelProperty("状态")
    private Integer status;

}
