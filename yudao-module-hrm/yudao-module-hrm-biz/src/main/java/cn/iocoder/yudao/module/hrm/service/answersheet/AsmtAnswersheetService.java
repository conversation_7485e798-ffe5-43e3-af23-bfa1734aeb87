package cn.iocoder.yudao.module.hrm.service.answersheet;

import cn.iocoder.yudao.framework.common.pojo.PageResult;
import cn.iocoder.yudao.module.hrm.controller.admin.answersheet.vo.*;
import cn.iocoder.yudao.module.hrm.dal.dataobject.answersheet.AsmtAnswersheetDO;
import jakarta.validation.Valid;

import java.util.List;

/**
 * 用户答题卡 Service 接口
 *
 * <AUTHOR>
 */
public interface AsmtAnswersheetService {

    /**
     * 创建用户答题卡
     *
     * @param list 答题列表
     */
    void createAsmtAnswersheet(@Valid List<AsmtAnswersheetSaveReqVO> list);

    /**
     * 更新用户答题卡
     *
     * @param updateReqVO 更新信息
     */
    void updateAsmtAnswersheet(@Valid AsmtAnswersheetSaveReqVO updateReqVO);

    /**
     * 删除用户答题记录
     * @param userTaskId 用户任务ID
     * @param paperId 试卷ID
     */
    void deleteByUserTaskIdAndPaperId(Long userTaskId, Long paperId);

    /**
     * 检查某份试卷用户是否已经提交了答案，目前是只要提交了就算已经回答了，不需要检测是否已全部回答
     * @param userId 用户ID
     * @param userTaskId 任务与用户关联表的ID
     * @param pkgId 测评包ID，因为测评包可能会与多个任务关联
     * @param paperId 试卷ID，同一份试卷可能与多个测评包关联
     * @return 结果
     */
    boolean checkUserIsAnswer(Long userId, Long userTaskId, Long pkgId, Long paperId);

    /**
     * 获得用户答题卡
     *
     * @param id 编号
     * @return 用户答题卡
     */
    AsmtAnswersheetDO getAsmtAnswersheet(Long id);

    /**
     * 获得用户答题卡分页
     *
     * @param pageReqVO 分页查询
     * @return 用户答题卡分页
     */
    PageResult<AsmtAnswersheetDO> getAsmtAnswersheetPage(AsmtAnswersheetPageReqVO pageReqVO);

    PageResult<AsmtAnswersheetSearchRespVO> searchPage(AsmtAnswersheetSearchReqVO reqVO);
    List<AsmtAnswersheetRespVO> selectListByUserTaskId(Long userTaskId, Long paperId);

    List<AsmtAnswersheetDO> selectListByTaskUser(Long taskId, Long userId);

    List<Long> getAllUserIdsByTaskId(Long taskId);
}
