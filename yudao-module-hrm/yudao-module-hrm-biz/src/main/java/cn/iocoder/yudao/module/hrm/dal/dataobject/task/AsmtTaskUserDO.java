package cn.iocoder.yudao.module.hrm.dal.dataobject.task;

import cn.iocoder.yudao.framework.mybatis.core.dataobject.BaseDO;
import com.baomidou.mybatisplus.annotation.KeySequence;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.*;

/**
 * 测评任务-任务用户关联 DO
 *
 * <AUTHOR>
 */
@TableName("hrm_asmt_task_user")
@KeySequence("hrm_asmt_task_user_seq") // 用于 Oracle、PostgreSQL、Kingbase、DB2、H2 数据库的主键自增。如果是 MySQL 等数据库，可不写。
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class AsmtTaskUserDO extends BaseDO {

    /**
     * 标签id
     */
    @TableId
    private Long id;
    /**
     * 测评任务id
     */
    private Long taskId;
    /**
     * 用户id
     */
    private Long userId;

    /**
     * 完成状态，0未完成，1已完成
     */
    private Integer status;
}
