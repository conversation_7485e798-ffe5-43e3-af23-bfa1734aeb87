package cn.iocoder.yudao.module.hrm.controller.admin.paper.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotNull;
import lombok.Data;

@Schema(description = "管理后台 - 测评卷与测评题关联新增/修改 Request VO")
@Data
public class AsmtPaperQuestionSaveReqVO {

    @Schema(description = "id", requiredMode = Schema.RequiredMode.REQUIRED, example = "29593")
    private Long id;

    @Schema(description = "试卷", requiredMode = Schema.RequiredMode.REQUIRED, example = "32105")
    @NotNull(message = "试卷不能为空")
    private Long paperId;

    @Schema(description = "测评题", requiredMode = Schema.RequiredMode.REQUIRED, example = "23009")
    @NotNull(message = "测评题不能为空")
    private Long quesId;

    @Schema(description = "显示顺序")
    private Integer sort;

}