<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="cn.iocoder.yudao.module.hrm.dal.mysql.paper.AsmtPaperQuestionMapper">
    <select id="getByPaperId" resultType="cn.iocoder.yudao.module.hrm.controller.admin.paper.vo.AsmtPaperQuestionRespVO">
        SELECT a.*, b.title AS ques_title
        FROM hrm_asmt_paper_question a
                 LEFT JOIN hrm_asmt_question b ON a.ques_id=b.id
        WHERE a.paper_id=#{paperId} AND a.deleted=0 AND b.`status`=0
        ORDER BY sort
    </select>
    <!--
        一般情况下，尽可能使用 Mapper 进行 CRUD 增删改查即可。
        无法满足的场景，例如说多表关联查询，才使用 XML 编写 SQL。
        代码生成器暂时只生成 Mapper XML 文件本身，更多推荐 MybatisX 快速开发插件来生成查询。
        文档可见：https://www.iocoder.cn/MyBatis/x-plugins/
     -->

</mapper>
