<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="cn.iocoder.yudao.module.hrm.dal.mysql.answersheet.AsmtAnswersheetMapper">

    <select id="searchPage"
            resultType="cn.iocoder.yudao.module.hrm.controller.admin.answersheet.vo.AsmtAnswersheetSearchRespVO">
        SELECT CONCAT(a.user_id,'-',a.task_id,'-',a.paper_id) AS id, a.*,ta.pkg_id, ta.task_name, ta.`status` AS
        task_status,u.nickname , p.paper_name, p.minutes, p.asmt_type,p.`status` AS paper_status
        FROM (
        SELECT user_id, task_id, paper_id,MIN(create_time) AS create_time, MIN(user_task_id) AS user_task_id
        FROM hrm_asmt_answersheet
        <where>
            deleted=0
            <if test="reqVO.taskId != null">
                AND task_id=#{reqVO.taskId}
            </if>
            <if test="reqVO.userId != null">
                AND user_id=#{reqVO.userId}
            </if>
            <if test="reqVO.paperId != null">
                AND paper_id=#{reqVO.paperId}
            </if>
        </where>
        GROUP BY user_id, task_id, paper_id
        ) a
        LEFT JOIN hrm_asmt_task ta ON a.task_id=ta.id
        LEFT JOIN system_users u ON a.user_id=u.id
        LEFT JOIN hrm_asmt_paper p ON a.paper_id=p.id
        <where>
            <if test="reqVO.nickname != null and reqVO.nickname !=''">
                AND u.nickname LIKE CONCAT('%', #{reqVO.nickname}, '%')
            </if>
            <if test="reqVO.taskName != null and reqVO.taskName !=''">
                AND ta.task_name LIKE CONCAT('%', #{reqVO.taskName}, '%')
            </if>
            <if test="reqVO.paperName != null and reqVO.paperName !=''">
                AND p.paper_name LIKE CONCAT('%', #{paperName}, '%')
            </if>
        </where>
        order by a.task_id desc , a.user_id asc ,a.paper_id asc
        LIMIT #{reqVO.pageNo}, #{reqVO.pageSize}
    </select>
    <select id="searchCount" resultType="java.lang.Long">
        SELECT COUNT(1)
        FROM (
        SELECT user_id, task_id, paper_id,MIN(create_time) AS create_time, MIN(user_task_id) AS user_task_id
        FROM hrm_asmt_answersheet
        <where>
            deleted=0
            <if test="reqVO.taskId != null">
                AND task_id=#{reqVO.taskId}
            </if>
            <if test="reqVO.userId != null">
                AND user_id=#{reqVO.userId}
            </if>
            <if test="reqVO.paperId != null">
                AND paper_id=#{reqVO.paperId}
            </if>
        </where>
        GROUP BY user_id, task_id, paper_id
        ) a
        LEFT JOIN hrm_asmt_task ta ON a.task_id=ta.id
        LEFT JOIN system_users u ON a.user_id=u.id
        LEFT JOIN hrm_asmt_paper p ON a.paper_id=p.id
        <where>
            <if test="reqVO.nickname != null and reqVO.nickname !=''">
                AND u.nickname LIKE CONCAT('%', #{reqVO.nickname}, '%')
            </if>
            <if test="reqVO.taskName != null and reqVO.taskName !=''">
                AND ta.task_name LIKE CONCAT('%', #{reqVO.taskName}, '%')
            </if>
            <if test="reqVO.paperName != null and reqVO.paperName !=''">
                AND p.paper_name LIKE CONCAT('%', #{paperName}, '%')
            </if>
        </where>
    </select>

    <select id="selectListByUserTaskId"
            resultType="cn.iocoder.yudao.module.hrm.controller.admin.answersheet.vo.AsmtAnswersheetRespVO">
        SELECT a.*, q.title AS ques_title, q.quse_code
        FROM hrm_asmt_answersheet a
        LEFT JOIN hrm_asmt_question q ON a.ques_id = q.id
        <where>
            a.deleted=0
            <if test="userTaskId != null">
                AND a.user_task_id = #{userTaskId}
            </if>
            <if test="paperId != null">
                AND a.paper_id = #{paperId}
            </if>
        </where>
        ORDER BY a.ques_sort
    </select>

    <select id="getUserCountByTaskId" resultType="java.lang.Long">
        SELECT
            COUNT( DISTINCT user_id )
        FROM (
                 SELECT
                     user_id
                 FROM
                     hrm_asmt_answersheet
                <where>
                     task_id = #{taskId} AND deleted=0
                    <if test="userId != null">
                        AND user_id = #{userId}
                    </if>
                </where>
                 GROUP BY
                     user_id
                 HAVING COUNT( DISTINCT paper_id ) = (
                     SELECT COUNT(DISTINCT b.id) FROM hrm_asmt_task a INNER JOIN hrm_asmt_package_paper b ON a.pkg_id=b.pkg_id
                     WHERE a.id=#{taskId} AND b.deleted=0
                 )
             ) AS completed_num
    </select>
</mapper>
