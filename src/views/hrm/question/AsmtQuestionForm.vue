<template>
  <Dialog :title="dialogTitle" v-model="dialogVisible" :scroll="true" width="70%">
    <div v-loading="formLoading">
      <el-form
        ref="formRef"
        :model="formData"
        :rules="formRules"
        label-width="100px"
      >
        <el-form-item label="题目" prop="title">
          <el-input v-model="formData.title" type="textarea" placeholder="请输入题目"/>
        </el-form-item>
        <el-form-item label="测评类型" prop="asmtType">
          <el-select v-model="formData.asmtType" placeholder="请选择测评类型">
            <el-option
              v-for="dict in getStrDictOptions(DICT_TYPE.HRM_ASMT_TYPE)"
              :key="dict.value"
              :label="dict.label"
              :value="dict.value"
            />
          </el-select>
        </el-form-item>
        <el-form-item label="题型" prop="quesType">
          <el-select v-model="formData.quesType" placeholder="请选择题型">
            <el-option
              v-for="dict in getStrDictOptions(DICT_TYPE.HRM_ASMT_QUES_TYPE)"
              :key="dict.value"
              :label="dict.label"
              :value="dict.value"
            />
          </el-select>
        </el-form-item>
        <el-form-item label="标签" prop="tags">
          <el-input v-model="formData.tags" placeholder="请输入标签"/>
        </el-form-item>
        <el-form-item label="状态" prop="status">
          <el-radio-group v-model="formData.status">
            <el-radio
              v-for="dict in getIntDictOptions(DICT_TYPE.COMMON_STATUS)"
              :key="dict.value"
              :label="dict.value"
            >
              {{ dict.label }}
            </el-radio>
          </el-radio-group>
        </el-form-item>
        <el-form-item label="题目说明" prop="description">
          <el-input v-model="formData.description" type="textarea" placeholder="题目说明"/>
        </el-form-item>
      </el-form>
      <el-tabs>
        <el-tab-pane label="题目选项">
          <AsmtQuestionOptionForm ref="optFormRef" :quse-id="formData.id" />
        </el-tab-pane>
      </el-tabs>
    </div>

    <!-- 快速录入对话框 -->
    <el-dialog
      v-model="quickInputVisible"
      title="快速录入"
      width="50%"
      append-to-body
    >
      <el-form>
        <el-form-item>
          <el-input
            v-model="quickInputContent"
            type="textarea"
            :rows="8"
            placeholder="第一行为题目，剩余行为选项内容，每行一个选项"
          />
        </el-form-item>
      </el-form>
      <template #footer>
        <el-button @click="handleQuickInput" type="primary">确 定</el-button>
        <el-button @click="quickInputVisible = false">取 消</el-button>
      </template>
    </el-dialog>

    <template #footer>
      <el-button @click="openQuickInput" type="success">快速录入</el-button>
      <el-button @click="submitForm" type="primary" :disabled="formLoading">确 定</el-button>
      <el-button @click="dialogVisible = false">取 消</el-button>
    </template>
  </Dialog>
</template>
<script setup lang="ts">
import {getIntDictOptions, getStrDictOptions, DICT_TYPE} from '@/utils/dict'
import {AsmtQuestionApi} from '@/api/hrm/question'
import AsmtQuestionOptionForm from "@/views/hrm/question-option/AsmtQuestionOptionForm.vue";

/** 测评题 表单 */
defineOptions({name: 'AsmtQuestionForm'})

const {t} = useI18n() // 国际化
const message = useMessage() // 消息弹窗

const dialogVisible = ref(false) // 弹窗的是否展示
const dialogTitle = ref('') // 弹窗的标题
const formLoading = ref(false) // 表单的加载中：1）修改时的数据加载；2）提交的按钮禁用
const formType = ref('') // 表单的类型：create - 新增；update - 修改
const formData = ref({
  id: undefined,
  status: 0,
  quseCode: undefined,
  asmtType: undefined,
  quesType: undefined,
  title: undefined,
  description: undefined,
  tags: undefined
})

// 快速录入相关
const quickInputVisible = ref(false)
const quickInputContent = ref('')

// 打开快速录入对话框
const openQuickInput = () => {
  quickInputVisible.value = true
  quickInputContent.value = ''
}

// 处理快速录入内容
const handleQuickInput = () => {
  if (!quickInputContent.value.trim()) {
    message.warning('请输入内容')
    return
  }

  const lines = quickInputContent.value.trim().split('\n')
  if (lines.length < 2) {
    message.warning('内容格式不正确，至少需要一个题目和一个选项')
    return
  }

  // 设置题目为第一行
  formData.value.title = lines[0]

  // 清空现有选项
  const options = optFormRef.value.getData()
  while (options.length > 0) {
    options.pop()
  }

  // 添加新选项
  for (let i = 1; i < lines.length; i++) {
    const optionName = lines[i].replace(/^[A-Z]\s*/, '').trim()
    if (optionName) {
      // 添加新选项
      optFormRef.value.handleAdd()
      const options = optFormRef.value.getData()
      options[options.length - 1].optionName = optionName
    }
  }

  quickInputVisible.value = false
  message.success('快速录入成功')
}

const formRules = reactive({
  title: [{required: true, message: '题目不能为空', trigger: 'blur'}],
  asmtType: [{required: true, message: '测评类型不能为空', trigger: 'blur'}]
})
const formRef = ref() // 表单 Ref
const optFormRef = ref() // 选项表单 Ref


/** 打开弹窗 */
const open = async (type: string, id?: number) => {
  dialogVisible.value = true
  dialogTitle.value = t('action.' + type)
  formType.value = type
  resetForm()
  // 修改时，设置数据
  if (id) {
    formLoading.value = true
    try {
      formData.value = await AsmtQuestionApi.getAsmtQuestion(id)
    } finally {
      formLoading.value = false
    }
  }
}
defineExpose({open}) // 提供 open 方法，用于打开弹窗

/** 提交表单 */
const emit = defineEmits(['success']) // 定义 success 事件，用于操作成功后的回调
const submitForm = async () => {
  // 校验表单
  await formRef.value.validate()
  try {
    // 校验选项表单
    await optFormRef.value.validate()
  } catch (e) {
    return
  }

  // 提交请求
  formLoading.value = true
  try {
    if (formType.value === 'create') {
      await AsmtQuestionApi.createAsmtQuestion({
        question: formData.value,
        options: optFormRef.value.getData()
      })
      message.success(t('common.createSuccess'))
    } else {
      await AsmtQuestionApi.updateAsmtQuestion({
        question: formData.value,
        options: optFormRef.value.getData()
      })
      message.success(t('common.updateSuccess'))
    }
    dialogVisible.value = false
    // 发送操作成功的事件
    emit('success')
  } finally {
    formLoading.value = false
  }
}

/** 重置表单 */
const resetForm = () => {
  formData.value = {
    id: undefined,
    status: 0,
    quseCode: undefined,
    asmtType: undefined,
    quesType: 'dx',
    title: undefined,
    description: undefined,
    tags: undefined
  }
  formRef.value?.resetFields()
}
</script>
