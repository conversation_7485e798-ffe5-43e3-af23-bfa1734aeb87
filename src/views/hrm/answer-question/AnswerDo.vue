<template>
  <div class="questions-list" v-loading="loading">
    <div class="background-img">
      <img src="@/assets/imgs/background.png"/>
    </div>
    <div class="questionnaire-container" v-if="isInit">
      <div class="progress-view">
        <el-progress :show-text="false" stroke-width="10" :percentage="progressValue"/>
      </div>
      <div class="ques-header">
        <div>{{ paperInfo?.paperName }}</div>
      </div>
      <div class="timer p-10px text-center">
        <span class="pr-25px">当前用户：{{ nickname }}</span>
        <el-icon>
          <Timer/>
        </el-icon>
        <span>剩余时间: {{ formattedTime }}</span>
        <el-button size="small" type="primary" style="margin-left: 20px" @click.stop="answerRandom">
          随机答题
        </el-button>
      </div>

      <el-form class="questionnaire-content"
               ref="formRef"
               :model="formData"
               label-position="top">
        <div class="paper-description">{{ paperDescription }}</div>
        <el-form-item
          v-for="(question, index) in formData"
          :key="question.quesId"
          :prop="`${index}.answerId`"
          :rules="[{ required: true, message: '请选择一个选项', trigger: 'change' }]"
        >
          <template #label>
            <label class="question-title">{{ index + 1 }}. {{ question.quesTitle }}</label>
          </template>
          <el-radio-group v-model="question.answerId">
            <el-radio v-for="o in question.optionList"
                      :key="o.id"
                      :value="o.id"
                      size="large"
                      border>
              {{ o.optionName }}
            </el-radio>
          </el-radio-group>

        </el-form-item>
      </el-form>
      <div v-if="formData.length > 0" class="bottom-button">
        <el-button type="primary" @click.stop="submitForm">提交测试</el-button>
      </div>
    </div>
  </div>

  <!-- 倒计时结束提示 -->
  <el-dialog
    v-model="timeoutDialogVisible"
    title="提示"
    :width="`${answerDialogWidth}px`"
    :show-close="false"
    :close-on-click-modal="false"
    :close-on-press-escape="false"
    append-to-body
  >
    <span>问卷时间已结束，系统将自动提交您的答案</span>
    <template #footer>
      <el-button type="primary" @click="handleTimeoutSubmit">确定</el-button>
    </template>
  </el-dialog>

  <!-- 页面切换警告 -->
  <el-dialog
    v-model="tabSwitchWarningVisible"
    title="警告"
    :width="`${answerDialogWidth}px`"
    append-to-body
  >
    <span>检测到您切换了页面，请专注于当前问卷</span>
    <template #footer>
      <el-button type="primary" @click="tabSwitchWarningVisible = false">确定</el-button>
    </template>
  </el-dialog>
</template>

<script setup>
import {ref, reactive, computed, onMounted, onUnmounted} from 'vue'
import {ElMessage, ElMessageBox} from 'element-plus'
import {AsmtPaperQuestionApi} from "@/api/hrm/paper-question";
import {AsmtAnswersheetApi} from "@/api/hrm/answersheet";
import {Timer} from '@element-plus/icons-vue'
import {number, string} from "vue-types";

const message = useMessage() // 消息弹窗

defineOptions({name: 'AnswerDo'})

const loading = ref(false)
const isInit = ref(false)
// 表单数据
const formData = ref([])
// 表单引用
const formRef = ref()
// 弹框宽度
const answerDialogWidth = computed(() => {
  return Math.max(document.documentElement.clientWidth * 0.3, 300)
})
// 已答题数量
const answeredCount = computed(() => {
  return formData.value.filter(item => item.answerId != null).length
})
// 进度条
const progressValue = computed(() => {
  if (answeredCount.value === 0) return 0;
  return answeredCount.value / formData.value.length * 100;
})
const props = defineProps({
  paperInfo: { // 试卷数据
    type: Object,
    default: () => {
      return {}
    }
  },
  taskId: { // 评测任务ID
    type: number,
    default: () => null
  },
  userTaskId: { // 任务与用户表关联的ID
    type: number,
    default: () => null
  },
  nickname: {
    type: string,
    default: () => ''
  },
})

// 倒计时相关
const remainingTime = ref(100000)
const timer = ref(null)
const timeoutDialogVisible = ref(false)

// 格式化时间显示
const formattedTime = computed(() => {
  const minutes = Math.floor(remainingTime.value / 60)
  const seconds = remainingTime.value % 60
  return `${minutes.toString().padStart(2, '0')}:${seconds.toString().padStart(2, '0')}`
})

const paperDescription = computed(() => {
  if (props.paperInfo.paperDescription && props.paperInfo.paperDescription.length > 0) {
    return props.paperInfo.paperDescription
  }

  return '以下题目均为单选题，请根据实际情况选择。'
})

// 页面切换检测
const tabSwitchWarningVisible = ref(false)
let lastActivityTime = Date.now()

// 开始倒计时
const startTimer = () => {
  if (props.paperInfo.minutes) {
    remainingTime.value = 303//props.paperInfo.minutes * 60

    timer.value = setInterval(() => {
      remainingTime.value--
      if (remainingTime.value === 0) {
        clearInterval(timer.value)

        // 不允许超时
        if (props.paperInfo.answerLimit === 0) {
          doSubmitForm() // 提交
          timeoutDialogVisible.value = true
        }
      }

      if (remainingTime.value === 300) {
        message.warning("还剩下5分钟，请合理安排时间！")
      }
    }, 1000)
  }
}

// 处理超时提交
const handleTimeoutSubmit = () => {
  timeoutDialogVisible.value = false
  // submitForm()
}

const emit = defineEmits(['success']) // 定义 success 事件，用于操作成功后的回调
// 提交表单
const submitForm = async () => {
  try {
    await formRef.value.validate()
  } catch (err) {
    ElMessage.error('请完成所有题目')
    return;
  }
  await message.confirm('是否确认提交评测结果，请注意，本次评测只有一次提交机会，一旦提交将不能修改', '提交评测结果')
  await doSubmitForm()
}

const doSubmitForm = async () => {
  try {
    const res = JSON.parse(JSON.stringify(formData.value));
    res.forEach(r => {
      delete r.quesTitle
      delete r.optionList
    })
    clearInterval(timer.value)
    loading.value = true;
    await AsmtAnswersheetApi.createAsmtAnswersheet(res)

    message.success('提交成功')
    setTimeout(() => {
      emit('success', res)
    }, 1500)
  } finally {
    loading.value = false;
  }
}

// 检测页面活跃状态
const checkActivity = () => {
  const now = Date.now()
  // 如果超过5秒没有活动，认为用户切换了页面
  if (now - lastActivityTime > 5000) {
    // tabSwitchWarningVisible.value = true // TODO
  }
  lastActivityTime = now
}

/** 随机答题 - 测试时使用 */
const answerRandom = async () => {
  await message.confirm('该操作将会把每道题随机选一个选项提交，是否继续')
  console.log(formData.value)

  formData.value.forEach(item => {
    const index = Math.floor(Math.random() * item.optionList.length)
    item.answerId = item.optionList[index].id
  })

  await submitForm();
}

// 页面活动事件监听
const setupActivityListeners = () => {
  window.addEventListener('mousemove', checkActivity)
  window.addEventListener('keydown', checkActivity)
  window.addEventListener('scroll', checkActivity)
  window.addEventListener('click', checkActivity)
}

// 移除事件监听
const removeActivityListeners = () => {
  window.removeEventListener('mousemove', checkActivity)
  window.removeEventListener('keydown', checkActivity)
  window.removeEventListener('scroll', checkActivity)
  window.removeEventListener('click', checkActivity)
}

const getQuestions = async () => {
  try {
    loading.value = true;
    const data = await AsmtPaperQuestionApi.getPaperQuestionAndOptions(props.paperInfo.paperId)
    const formItems = []
    data.forEach(q => {
      // userId
      formItems.push({
        taskId: props.taskId,
        userTaskId: props.userTaskId,
        pkgId: props.paperInfo.pkgId,
        paperId: props.paperInfo.paperId,
        quesId: q.quesId,
        quesSort: q.sort,
        answerId: null,
        answer: null,
        score: null,
        quesTitle: q.quesTitle,
        optionList: q.optionList || []
      })
    })
    formData.value = formItems
    // startTimer()
  } finally {
    loading.value = false;
  }
}

/** 检查是否重复进入 */
const checkRepeat = async () => {
  if (props.paperInfo.answerLimit === 0 || props.paperInfo.answerLimit === 1) {
    const res = await AsmtAnswersheetApi.getAnswerMark(props.userTaskId, props.paperInfo.paperId)
    if (res != null) {
      await ElMessageBox.alert('您的评测机会已用完，请联系管理员！', '提示', {
        confirmButtonText: '好的',
        callback: () => {
          window.close()
        },
      })
    }
  }
}

const init = async () => {
  await checkRepeat()
  await getQuestions();
  setupActivityListeners()

  let minutesTips = '';
  if (props.paperInfo.minutes) {
    minutesTips = `您需要在${props.paperInfo.minutes}分钟内完成，且`
  }

  let tipText = ''
  // 0既不能重复进入也不允许超时；1允许超时但不允许重复进入；2可以超时也可以重复进入
  if (props.paperInfo.answerLimit === 0 || props.paperInfo.answerLimit === 1) {
    tipText = `是否确定现在开始测试，该测试仅能作答一次，请确保您的网络顺畅，一旦开始测试，${minutesTips}中途不能退出！`
  } else if (props.paperInfo.answerLimit === 2) {
    tipText = '是否确定现在开始测试，该测试仅能作答一次，请确保您的网络顺畅！'
  }

  message.confirm(tipText, '开始测试')
    .then(() => {
      startTimer()
      isInit.value = true

      if (props.paperInfo.answerLimit === 0 || props.paperInfo.answerLimit === 1) {
        AsmtAnswersheetApi.addAnswerMark(props.userTaskId, props.paperInfo.paperId)
      }
    })
    .catch(() => {
      window.close()
    })

  // 每分钟检查一次活动状态
  setInterval(checkActivity, 60000)
}

// 组件挂载时
onMounted(() => {
  init()
})

// 组件卸载时
onUnmounted(() => {
  if (timer.value) {
    clearInterval(timer.value)
  }
  removeActivityListeners()
})
</script>
<style lang="scss">
.questions-list {
  margin: 30px auto;
  width: 100%;

  .background-img {
    position: fixed;
    top: 0;
    left: 0;
    width: 100vw;
    z-index: 0;

    img {
      width: 100%;
      height: 640px;
      overflow: hidden;
      object-fit: cover;
      object-position: top;
      mask-image: -webkit-gradient(linear, left top, left bottom, color-stop(63.25%, #000000), color-stop(66.25%, rgba(0, 0, 0, 0.97)), color-stop(69.25%, rgba(0, 0, 0, 0.94)), to(rgba(0, 0, 0, 0)));
      mask-image: linear-gradient(180deg, #000000 63.25%, rgba(0, 0, 0, 0.97) 66.25%, rgba(0, 0, 0, 0.94) 69.25%, rgba(0, 0, 0, 0) 100%);
    }
  }

  .header-fix {
    height: 100px;
  }
}

.questionnaire-container {
  //padding: 0 40px 40px;
  position: relative;
  margin: 0 auto;
  width: 100%;
  max-width: 900px;
  background-color: white;
  z-index: 1;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1); /* 添加阴影效果增强视觉感 */

  .ques-header {
    padding-top: 20px;
    font-size: 28px;
    color: black;
    text-align: center;
  }

  .paper-description {
    padding-bottom: 25px;
    word-break: break-all;
    white-space: pre-wrap; /* 保留所有空格和换行 */
  }

  .question-title {
    font-weight: normal;
    font-size: 18px;
    word-break: break-word;
    color: black;
  }

  .el-scrollbar {
    padding: 16px;
  }

  .questionnaire-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 30px;
  }

  .progress-view {
    padding: 5px 15px;
    position: sticky;
    top: 0;
    height: 50px;
    background: white;
    z-index: 2;
  }

  .timer {
    padding-left: 16px;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 16px;
    color: #666;
    position: sticky;
    top: 15px;
    background: white;
    z-index: 3;
    height: 50px;

    .el-icon {
      margin-right: 5px;
    }
  }

  .questionnaire-content {
    padding: 0 60px;
    margin-top: 40px;
    margin-bottom: 80px;
  }

  .el-form-item {
    margin-bottom: 35px;
  }

  .el-form-item__label {
    margin-bottom: 15px;

    &:before {
      font-size: 18px;
      font-weight: bold;
    }
  }

  .el-form-item__content {
    display: block !important;
  }

  .el-radio-group {
    display: block;
  }

  .el-radio {
    padding: 5px 19px 5px 11px !important;
    margin-right: 0;
    height: auto;
    display: flex;
    align-items: center;
    min-height: 40px;

    .el-radio__label {
      text-wrap: auto;
      line-height: 20px;
    }
  }

  .el-radio + .el-radio {
    margin-top: 10px;
  }

  .bottom-button {
    margin: 0 auto;
    padding-bottom: 40px;
    width: 80%;

    .el-button {
      width: 100%;
      height: 45px;
      font-size: 17px;
    }
  }
}
</style>
