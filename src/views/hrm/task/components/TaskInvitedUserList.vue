<template>
  <ContentWrap>
    <!-- 搜索工作栏 -->
    <el-form
      v-if="isSearchBar"
      class="-mb-15px"
      :model="queryParams"
      ref="queryFormRef"
      :inline="true"
      label-width="68px"
    >
      <el-form-item label="搜索" prop="keyword">
        <el-input
          v-model="queryParams.keyword"
          placeholder="姓名、手机号、邮箱"
          clearable
          @keyup.enter="handleQuery"
          class="!w-240px"
        />
      </el-form-item>
      <el-form-item>
        <el-button @click="handleQuery">
          <Icon icon="ep:search" class="mr-5px"/>
          搜索
        </el-button>
        <el-button @click="resetQuery">
          <Icon icon="ep:refresh" class="mr-5px"/>
          重置
        </el-button>
        <delayed-tooltip content="已存在本系统中的用户">
          <el-button @click="inviteButton" type="primary" v-hasPermi="['hrm:asmt-task:create','hrm:asmt-task:update']">
            <Icon icon="ep:plus"/>
            邀请系统用户
          </el-button>
        </delayed-tooltip>
        <delayed-tooltip content="第一次使用本系统的用户">
          <el-button @click="inviteOutsideButton" type="warning" v-hasPermi="['hrm:asmt-task:create','hrm:asmt-task:update']">
            <Icon icon="ep:plus"/>
            邀请外部用户
          </el-button>
        </delayed-tooltip>
        <el-button
          type="success"
          plain
          @click="handleExport"
          :loading="exportLoading"
          v-hasPermi="['hrm:asmt-task:export']"
        >
          <Icon icon="ep:download" class="mr-5px"/>
          导出
        </el-button>
      </el-form-item>
    </el-form>
    <template v-else>
      <el-button @click="resetQuery">
        <Icon icon="ep:refresh" class="mr-5px"/>
        刷新
      </el-button>
      <el-button @click="inviteButton" type="primary" v-hasPermi="['hrm:asmt-task:create','hrm:asmt-task:update']">
        <Icon icon="ep:plus"/>
        邀请
      </el-button>
      <el-button
        type="success"
        plain
        @click="handleExport"
        :loading="exportLoading"
        v-hasPermi="['hrm:asmt-task:export']"
      >
        <Icon icon="ep:download" class="mr-5px"/>
        导出
      </el-button>
    </template>
  </ContentWrap>

  <!-- 列表 -->
  <ContentWrap>
    <el-table
      ref="tableRef"
      :height="`calc(100vh - ${tableTop}px - 75px - 52px)`"
      v-loading="loading"
      :data="list"
      :stripe="true"
      :show-overflow-tooltip="true"
    >
      <el-table-column label="任务" prop="taskName"/>
      <el-table-column label="用户" prop="nickname"/>
      <el-table-column label="部门" prop="deptName"/>
      <el-table-column label="手机号" prop="mobile"/>
      <el-table-column label="邮箱" prop="email"/>
      <el-table-column
        label="创建时间"
        align="center"
        prop="createTime"
        :formatter="dateFormatter"
        width="180px"
      />
      <el-table-column label="操作" align="center" width="100px">
        <template #default="scope">
          <el-button
            link
            type="danger"
            @click="handleDelete(scope.row.id)"
            v-hasPermi="['hrm:asmt-task:update']"
          >
            删除
          </el-button>
        </template>
      </el-table-column>
    </el-table>
    <!-- 分页 -->
    <Pagination
      :total="total"
      v-model:page="queryParams.pageNo"
      v-model:limit="queryParams.pageSize"
      @pagination="getList"
    />
  </ContentWrap>

  <store-staff-table-select :selected-ids="selectedIds" ref="pickUserRef" @change="pickUserDone"/>
  <invite-outside-user-form :task-id="taskId" ref="outsideRef" @success="getList"/>
</template>

<script setup lang="ts">
import StoreStaffTableSelect
  from "@/views/mall/trade/delivery/pickUpStore/components/StoreStaffTableSelect.vue";
import InviteOutsideUserForm from "@/views/hrm/task/components/InviteOutsideUserForm.vue";
import {dateFormatter} from '@/utils/formatTime'
import download from '@/utils/download'
import {AsmtTaskUserApi, AsmtTaskUserVO} from '@/api/hrm/taskuser'
import {bool, number, string} from "vue-types";

/** 测评任务-任务用户关联 列表 */
defineOptions({name: 'TaskInvitedUserList'})

const message = useMessage() // 消息弹窗
const {t} = useI18n() // 国际化

const pickUserRef = ref() // 用户选择器
const tableRef = ref()
const tableTop = ref(100)
const loading = ref(true) // 列表的加载中
const list = ref<AsmtTaskUserVO[]>([]) // 列表的数据
const total = ref(0) // 列表的总页数
const props = defineProps({
  taskId: {
    type: number || string,
    default: () => null
  },
  isSearchBar: { // 是否工作台显示
    type: bool,
    default: () => true
  },
})
const queryParams = reactive({
  pageNo: 1,
  pageSize: 10,
  taskId: props.taskId,
  keyword: undefined,
})
const queryFormRef = ref() // 搜索的表单
const exportLoading = ref(false) // 导出的加载中

const selectedIds = computed(() => {
  return list.value.map(item => item.userId);
})

/** 查询列表 */
const getList = async () => {
  loading.value = true
  try {
    const data = await AsmtTaskUserApi.search(queryParams)
    list.value = data.list
    total.value = data.total
  } finally {
    loading.value = false
  }
}

/** 邀请按钮 */
const inviteButton = async () => {
  pickUserRef.value.open()
}

const outsideRef = ref()
const inviteOutsideButton = async () => {
  outsideRef.value.open()
}

/** 用户选择器回调 */
const pickUserDone = async (row) => {
  // row
  const params = row.map(u => {
    return {
      taskId: Number(props.taskId),
      userId: u.id
    }
  })
  await AsmtTaskUserApi.createAsmtTaskUserBatch(params)
  message.success('邀请成功')
  await getList()
}

/** 搜索按钮操作 */
const handleQuery = () => {
  queryParams.pageNo = 1
  getList()
}

/** 重置按钮操作 */
const resetQuery = () => {
  queryFormRef.value.resetFields()
  handleQuery()
}

/** 删除按钮操作 */
const handleDelete = async (id: number) => {
  try {
    // 删除的二次确认
    await message.delConfirm()
    // 发起删除
    await AsmtTaskUserApi.deleteAsmtTaskUser(id)
    message.success(t('common.delSuccess'))
    // 刷新列表
    await getList()
  } catch {
  }
}

/** 导出按钮操作 */
const handleExport = async () => {
  try {
    // 导出的二次确认
    await message.exportConfirm()
    // 发起导出
    exportLoading.value = true
    const data = await AsmtTaskUserApi.exportAsmtTaskUser(queryParams)
    download.excel(data, '测评任务-任务用户关联.xls')
  } catch {
  } finally {
    exportLoading.value = false
  }
}

/** 初始化 **/
onMounted(() => {
  nextTick(() => {
    if (tableRef.value) {
      const rect = tableRef.value.$el.getBoundingClientRect();
      tableTop.value = rect.top
    }
  })
  getList()
})


defineExpose({handleQuery})
</script>
