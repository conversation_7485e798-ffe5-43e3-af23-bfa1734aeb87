<template>
  <ContentWrap>
    <!-- 搜索工作栏 -->
    <el-form
      class="-mb-15px"
      :model="queryParams"
      ref="queryFormRef"
      :inline="true"
      label-width="0"
    >
      <el-form-item label="" prop="onlyAnswer">
        <el-select
          v-model="queryParams.onlyAnswer"
          placeholder="请选择"
          class="!w-240px"
          @change="getData"
        >
          <el-option label="只看已答题的" :value="true"/>
          <el-option label="查看全部" :value="false"/>
        </el-select>
      </el-form-item>
      <el-form-item>
        <el-button @click="getData">
          <Icon icon="ep:refresh" class="mr-5px"/>
          刷新
        </el-button>
      </el-form-item>
    </el-form>
  </ContentWrap>
  <ContentWrap>
    <el-table v-loading="loading" :data="dataList" :stripe="true" :show-overflow-tooltip="true">
      <el-table-column label="任务" align="left" prop="taskName"/>
      <el-table-column label="姓名" align="left" prop="nickname"/>
      <el-table-column label="部门" align="left" prop="deptName"/>
      <el-table-column label="电子邮箱" align="left" prop="email"/>
      <el-table-column label="完成情况" align="center" prop="isAnswer" width="120">
        <template #default="{row}">
          <el-tag size="small" type="success" v-if="row.isAnswer">已完成评测</el-tag>
          <el-tag size="small" type="danger" v-else>未完成评测</el-tag>
        </template>
      </el-table-column>
      <el-table-column label="操作" align="center" width="280">
        <template #default="{row}">
          <el-button
            v-if="row.isAnswer"
            link
            type="primary"
            @click="showAnswerList(row.userId)"
          >
            查看
          </el-button>
          <el-button
            v-if="row.isAnswer"
            link
            type="primary"
            @click="calcIndexData(row.userId)"
          >
            指标计算
          </el-button>
          <el-button
            v-if="row.isAnswer"
            link
            type="success"
            @click="showReport(row.userId)"
          >
            查看报告
          </el-button>
        </template>
      </el-table-column>
    </el-table>
  </ContentWrap>
</template>
<script setup lang="ts">
import {AsmtTaskUserApi} from "@/api/hrm/taskuser";
import {AsmtIndexResultApi} from '@/api/hrm/indexresult'

const {params} = useRoute()
const {push} = useRouter() // 路由
const message = useMessage() // 消息弹窗

defineOptions({name: 'TaskUsersList'})

const loading = ref(false)
const drawerVisible = ref(false)
const queryParams = reactive({
  taskId: undefined,
  onlyAnswer: undefined
})
const dataList = ref([])
const getData = async () => {
  loading.value = true;
  try {
    dataList.value = await AsmtTaskUserApi.listByTask(queryParams.taskId, queryParams.onlyAnswer)
  } finally {
    loading.value = false
  }
}

/** 查看答题用户列表 */
const showAnswerList = (userId: number) => {
  push(`/asmt/asmt-answersheet?taskId=${queryParams.taskId}&userId=${userId}`)
}

/** 指标计算 */
const calcIndexData = async (userId: number) => {
  await AsmtIndexResultApi.calcIndexResult(queryParams.taskId, userId)
  message.success('计算成功')
}

/** 查看报告 */
const showReport = (userId: number) => {
  // TODO 根据测评报告跳转不同报告
  push(`/asmt/report/comprehensive/${queryParams.taskId}/${userId}`)
}

onMounted(() => {
  queryParams.taskId = params.taskId;
  queryParams.onlyAnswer = params.onlyAnswer == '1'

  dataList.value = []
  drawerVisible.value = true
  getData()
})
</script>

<style scoped lang="scss">

</style>
