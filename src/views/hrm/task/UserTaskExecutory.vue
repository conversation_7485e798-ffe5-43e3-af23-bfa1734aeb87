<template>
  <ContentWrap>
    <!-- 搜索工作栏 -->

    <el-button @click="handleQuery">
      <Icon icon="ep:refresh" class="mr-5px"/>
      刷新
    </el-button>
  </ContentWrap>

  <!-- 列表 -->
  <ContentWrap>
    <el-table
      ref="tableRef"
      v-loading="loading"
      :data="list"
      :stripe="true"
      :show-overflow-tooltip="true"
    >
      <el-table-column label="姓名" prop="nickname"/>
      <el-table-column label="工号" prop="jobNumber"/>
      <el-table-column label="岗位序列" prop="positionSeries"/>
      <el-table-column label="职级" prop="jobLevel"/>
      <el-table-column label="试卷" prop="paperName"/>
      <el-table-column
        label="创建时间"
        align="center"
        prop="createTime"
        :formatter="dateFormatter"
        width="180px"
      />
      <el-table-column label="操作" align="center" width="200px">
        <template #default="{row}">
          <el-button
            link
            type="danger"
            @click="removeMark(row)"
          >
            移除
          </el-button>
        </template>
      </el-table-column>
    </el-table>
    <!-- 分页 -->
  </ContentWrap>
</template>

<script setup lang="ts">
import {dateFormatter} from '@/utils/formatTime'
import {AsmtTaskUserApi} from '@/api/hrm/taskuser'

const message = useMessage() // 消息弹窗

/** 测评报告列表 */
defineOptions({name: 'UserTaskExecutory'})

const tableRef = ref()
const loading = ref(true) // 列表的加载中
const list = ref([]) // 列表的数据

/** 查询列表 */
const getList = async () => {
  loading.value = true
  try {
    list.value = await AsmtTaskUserApi.getAnsweringList()
  } finally {
    loading.value = false
  }
}

const removeMark = async (row: any) => {
  await message.delConfirm()

  await AsmtTaskUserApi.deleteAnswerMark(row.userTaskId, row.paperId)

  message.success('操作成功')

  await getList()
}

/** 搜索按钮操作 */
const handleQuery = () => {
  getList()
}

/** 初始化 **/
onMounted(() => {
  getList()
})


defineExpose({handleQuery})
</script>
