<template>
  <ContentWrap>
    <!-- 搜索工作栏 -->
    <el-form
      class="-mb-15px"
      :model="queryParams"
      ref="queryFormRef"
      :inline="true"
      label-width="58px"
    >
      <el-form-item label="用户">
        <el-select
          v-model="queryParams.userId"
          filterable
          remote
          :remote-method="getUserList"
          :loading="userLoading"
          placeholder="输入关键字"
          clearable
          remote-show-suffix
          reserve-keyword
          class="!w-180px"
        >
          <el-option
            v-for="item in userList"
            :key="item.id"
            :label="item.nickname"
            :value="item.id"
          >
            <div>
              {{ item.nickname }}
              <span
                style="font-size: 70%; color: var(--el-color-warning)">【{{ item.deptName }}】</span>
              <el-tag size="small" type="info" v-if="item.status == 1">已禁用</el-tag>
              <el-tag size="small" type="success" v-if="item.status == 0">启用中</el-tag>
            </div>
          </el-option>
        </el-select>
      </el-form-item>
      <!--      <el-form-item label="任务">-->
      <!--        <el-select-->
      <!--          v-model="queryParams.taskId"-->
      <!--          filterable-->
      <!--          remote-->
      <!--          :remote-method="getModelList"-->
      <!--          :loading="modelLoading"-->
      <!--          placeholder="输入关键字"-->
      <!--          clearable-->
      <!--          remote-show-suffix-->
      <!--          reserve-keyword-->
      <!--          class="!w-180px"-->
      <!--        >-->
      <!--          <el-option-->
      <!--            v-for="item in modelList"-->
      <!--            :key="item.id"-->
      <!--            :label="item.modelName"-->
      <!--            :value="item.id"-->
      <!--          />-->
      <!--        </el-select>-->
      <!--      </el-form-item>-->
      <el-form-item>
        <el-button @click="handleQuery">
          <Icon icon="ep:search" class="mr-5px"/>
          搜索
        </el-button>
        <el-button @click="resetQuery">
          <Icon icon="ep:refresh" class="mr-5px"/>
          重置
        </el-button>
        <el-button
          type="warning"
          plain
          @click="openImportDialog()"
          v-hasPermi="['hrm:asmt-model:create']"
        >
          <Icon icon="ep:upload" class="mr-5px"/>
          导入指标
        </el-button>
        <el-button
          type="success"
          plain
          @click="handleExport"
          :loading="exportLoading"
          v-hasPermi="['hrm:asmt-index-result:export']"
        >
          <Icon icon="ep:download" class="mr-5px"/>
          导出
        </el-button>
        <el-button
          plain
          :disabled="multipleSelection.length === 0"
          type="danger"
          @click="handleMultipleDelete()"
          v-hasPermi="['hrm:asmt-index-result:delete']"
        >
          批量删除
        </el-button>
      </el-form-item>
    </el-form>
  </ContentWrap>

  <!-- 列表 -->
  <ContentWrap>
    <el-table v-loading="loading"
              :data="list"
              :stripe="true"
              @selection-change="handleSelectionChange"
              :show-overflow-tooltip="true">
      <el-table-column type="selection"/>
      <el-table-column label="姓名" prop="userName"/>
      <el-table-column label="工号" prop="jobNumber"/>
      <el-table-column label="任务" prop="taskName"/>
      <el-table-column label="模型ID" prop="modelId" width="100"/>
      <el-table-column label="指标" prop="indexName"/>
      <el-table-column label="指标编码" prop="indexCode" width="80"/>
      <el-table-column label="指标分组" prop="indexGroup"/>
      <el-table-column label="得分" align="center" prop="score" width="100"/>
      <el-table-column label="标准分" align="center" prop="stdScore" width="100"/>
      <el-table-column
        label="更新时间"
        align="center"
        prop="updateTime"
        :formatter="dateFormatter"
        width="180px"
      />
      <el-table-column label="操作" align="center" width="120px">
        <template #default="scope">
          <el-button
            link
            type="primary"
            @click="openForm('update', scope.row.id)"
            v-hasPermi="['hrm:asmt-index-result:update']"
          >
            编辑
          </el-button>
          <el-button
            link
            type="danger"
            @click="handleDelete(scope.row.id)"
            v-hasPermi="['hrm:asmt-index-result:delete']"
          >
            删除
          </el-button>
        </template>
      </el-table-column>
    </el-table>
    <!-- 分页 -->
    <Pagination
      :total="total"
      v-model:page="queryParams.pageNo"
      v-model:limit="queryParams.pageSize"
      @pagination="getList"
    />
  </ContentWrap>

  <!-- 表单弹窗：添加/修改 -->
  <AsmtIndexResultForm ref="formRef" @success="getList"/>
  <index-result-import-form ref="importRef" @success="getList"/>
</template>

<script setup lang="ts">
import {dateFormatter} from '@/utils/formatTime'
import {getAccessToken} from '@/utils/auth'
import {AsmtIndexResultApi, AsmtIndexResultVO} from '@/api/hrm/indexresult'
import {AsmtModelApi} from '@/api/hrm/asmtmodel'
import AsmtIndexResultForm from './AsmtIndexResultForm.vue'
import * as UserApi from "@/api/system/user";
import IndexResultImportForm from "@/views/hrm/indexresult/IndexResultImportForm.vue";
import {ElTable} from "element-plus";

/** 测评指标计算结果 列表 */
defineOptions({name: 'AsmtIndexResult'})

const message = useMessage() // 消息弹窗
const {t} = useI18n() // 国际化

const loading = ref(true) // 列表的加载中
const list = ref<AsmtIndexResultVO[]>([]) // 列表的数据
const total = ref(0) // 列表的总页数
const queryParams = reactive({
  pageNo: 1,
  pageSize: 10,
  userId: undefined,
  taskId: undefined,
  modelId: undefined,
  indexId: undefined,
  score: undefined,
  stdScore: undefined,
  refValue: undefined,
  isImport: undefined,
  description: undefined,
  createTime: []
})
const currentUser = ref({})
const queryFormRef = ref() // 搜索的表单
const exportLoading = ref(false) // 导出的加载中

/** 查询列表 */
const getList = async () => {
  loading.value = true
  try {
    const data = await AsmtIndexResultApi.getAsmtIndexResultPage(queryParams)
    list.value = data.list
    total.value = data.total
  } finally {
    loading.value = false
  }
}


/** 导入指标 */
const importRef = ref()
const openImportDialog = () => {
  importRef.value.open()
}

/** 用户选项 */
const userList = ref([])
const userLoading = ref(false)
const lastUserQuery = ref(null)
const getUserList = async (query?: string) => {
  let params = {
    pageNo: 1,
    pageSize: 6,
    nickname: undefined
  };
  if (query && query.length > 0) {
    params.nickname = query.trim()
  }
  if (query == lastUserQuery.value && userList.value.length > 0) return;
  userLoading.value = true
  lastUserQuery.value = query
  try {
    const data = await UserApi.getUserPage(params)
    userList.value = data.list
  } finally {
    userLoading.value = false
  }
}

/** 模型选项 */
const modelList = ref([])
const modelLoading = ref(false)
const lastModelQuery = ref(null)
const getModelList = async (query?: string) => {
  let params = {
    pageNo: 1,
    pageSize: 6,
    modelName: undefined
  };
  if (query && query.length > 0) {
    params.modelName = query.trim()
  }
  if (query == lastModelQuery.value && modelList.value.length > 0) return;
  modelLoading.value = true
  lastModelQuery.value = query
  try {
    const data = await AsmtModelApi.getAsmtModelPage(params)
    modelList.value = data.list
  } finally {
    modelLoading.value = false
  }
}

/** 搜索按钮操作 */
const handleQuery = () => {
  queryParams.pageNo = 1
  getList()
}

/** 重置按钮操作 */
const resetQuery = () => {
  currentUser.value = {};
  queryFormRef.value.resetFields()
  handleQuery()
}

/** 添加/修改操作 */
const formRef = ref()
const openForm = (type: string, id?: number) => {
  formRef.value.open(type, id)
}

/** 删除按钮操作 */
const handleDelete = async (id: number) => {
  try {
    // 删除的二次确认
    await message.delConfirm()
    // 发起删除
    await AsmtIndexResultApi.deleteAsmtIndexResult(id)
    message.success(t('common.delSuccess'))
    // 刷新列表
    await getList()
  } catch {
  }
}

const multipleSelection = ref([])
const handleSelectionChange = (val: any) => {
  multipleSelection.value = val
}

const handleMultipleDelete = async () => {
  // 删除的二次确认
  await message.delConfirm()
  loading.value = true

  try {
    await AsmtIndexResultApi.deleteAsmtIndexResultBatch(multipleSelection.value.map(item => item.id))
    message.success(t('common.delSuccess'))
    // 刷新列表
    await getList()
  } finally {
    loading.value = false
  }
}

/** 导出按钮操作 */
const handleExport = async () => {
  await message.exportConfirm()

  let url = import.meta.env.VITE_BASE_URL + import.meta.env.VITE_API_URL + '/hrm/asmt-index-result/export?token=' + getAccessToken()
  if (queryParams.taskId) {
    url += '&taskId=' + queryParams.taskId
  }
  if (queryParams.userId) {
    url += '&userId=' + queryParams.userId
  }

  const link = document.createElement('a');
  link.href  = url;
  link.target  = '_blank';
  link.click();

  // /hrm/asmt-index-result/export
  // try {
  //   // 导出的二次确认
  //   await message.exportConfirm()
  //   // 发起导出
  //   exportLoading.value = true
  //   const data = await AsmtIndexResultApi.exportAsmtIndexResult(queryParams)
  //   download.excel(data, '测评指标计算结果.xls')
  // } catch {
  // } finally {
  //   exportLoading.value = false
  // }
}

/** 初始化 **/
onMounted(() => {
  getList()
})
</script>
