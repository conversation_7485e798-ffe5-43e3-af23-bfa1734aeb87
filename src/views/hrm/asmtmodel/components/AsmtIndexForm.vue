<template>
  <Dialog :title="dialogTitle" v-model="dialogVisible">
    <el-form
      ref="formRef"
      :model="formData"
      :rules="formRules"
      label-width="100px"
      v-loading="formLoading"
    >
      <el-form-item label="指标名称" prop="indexName">
        <el-input v-model="formData.indexName" placeholder="请输入指标名称" />
      </el-form-item>
      <el-form-item label="指标级别" prop="level" v-if="!formData.isImport">
        <el-input-number style="width: 100%;" v-model="formData.level" :min="1" :max="10"/>
      </el-form-item>
      <el-form-item label="指标编码" prop="indexCode">
        <el-input v-model="formData.indexCode" placeholder="请输入指标编码" />
      </el-form-item>
      <el-form-item label="标准分" prop="coverType">
        <el-select v-model="formData.coverType" placeholder="请选择">
          <el-option label="无标准分" :value="0"/>
          <el-option label="从映射表计算" :value="1"/>
          <el-option label="自定义公式" :value="2"/>
        </el-select>
      </el-form-item>
      <el-form-item label="所属分组" prop="indexGroup">
        <el-select v-model="formData.indexGroup"
                   filterable
                   allow-create
                   default-first-option
                   :reserve-keyword="false"
                   clearable>
          <el-option v-for="(n,i) in groupList"
                     :label="n"
                     :value="n"
                     :key="`index-g-${i}`" />
        </el-select>
      </el-form-item>
      <el-form-item label="显示顺序" prop="sort">
        <el-input v-model="formData.sort" placeholder="1" />
      </el-form-item>
      <el-form-item label="导入指标" prop="isImport" v-show="false">
        <el-radio-group v-model="formData.isImport" disabled>
          <el-radio
            v-for="dict in getBoolDictOptions(DICT_TYPE.INFRA_BOOLEAN_STRING)"
            :key="dict.value"
            :label="dict.value"
          >
            {{ dict.label }}
          </el-radio>
        </el-radio-group>
      </el-form-item>
      <el-form-item label="指标状态" prop="status">
        <el-radio-group v-model="formData.status">
          <el-radio
            v-for="dict in getIntDictOptions(DICT_TYPE.COMMON_STATUS)"
            :key="dict.value"
            :label="dict.value"
          >
            {{ dict.label }}
          </el-radio>
        </el-radio-group>
      </el-form-item>
      <el-form-item label="指标说明" prop="description">
        <el-input v-model="formData.description" type="textarea" placeholder="请输入说明" />
      </el-form-item>
    </el-form>
    <template #footer>
      <el-button @click="submitForm" type="primary" :disabled="formLoading">确 定</el-button>
      <el-button @click="dialogVisible = false">取 消</el-button>
    </template>
  </Dialog>
</template>
<script setup lang="ts">
import { AsmtModelApi } from '@/api/hrm/asmtmodel'
import {getIntDictOptions, DICT_TYPE, getBoolDictOptions} from '@/utils/dict'
const { t } = useI18n() // 国际化
const message = useMessage() // 消息弹窗
const props = defineProps({
  isImport: { // 是否显示导入指标
    type: Boolean,
    default: () => false
  }
})
const dialogVisible = ref(false) // 弹窗的是否展示
const dialogTitle = ref('') // 弹窗的标题
const formLoading = ref(false) // 表单的加载中：1）修改时的数据加载；2）提交的按钮禁用
const formType = ref('') // 表单的类型：create - 新增；update - 修改
const formData = ref({
  id: undefined,
  indexName: undefined,
  indexGroup: undefined,
  sort: 0,
  modelId: undefined,
  level: 1,
  coverType: 0,
  indexCode: undefined,
  rule: undefined,
  description: undefined,
  isImport: props.isImport,
  status: 1
})
const formRules = reactive({
  indexName: [{ required: true, message: '指标名称不能为空', trigger: 'blur' }],
  indexCode: [{ required: true, message: '指标编码不能为空', trigger: 'blur' }],
  level: [{ required: true, message: '级别不能为空', trigger: 'blur' }]
})
const groupList = ref([])
const formRef = ref() // 表单 Ref

/** 打开弹窗 */
const open = async (type: string, id?: number, modelId: number) => {
  dialogVisible.value = true
  dialogTitle.value = t('action.' + type) + '指标'
  formType.value = type
  resetForm()
  formData.value.modelId = modelId
  // 修改时，设置数据
  if (id) {
    formLoading.value = true
    try {
      formData.value = await AsmtModelApi.getAsmtIndex(id)
      await getIndexGroups()
    } finally {
      formLoading.value = false
    }
  }else {
    formLoading.value = true
    try {
      await getIndexGroups()
    } finally {
      formLoading.value = false
    }
  }
}
defineExpose({ open }) // 提供 open 方法，用于打开弹窗


const getIndexGroups = async () => {
  groupList.value = await AsmtModelApi.getIndexGroups(formData.value.modelId)
}

/** 提交表单 */
const emit = defineEmits(['success']) // 定义 success 事件，用于操作成功后的回调
const submitForm = async () => {
  // 校验表单
  await formRef.value.validate()
  // 提交请求
  formLoading.value = true
  try {
    const data = formData.value
    if (formData.value.isImport) {
      data.level = 0
    }
    if (formType.value === 'create') {
      await AsmtModelApi.createAsmtIndex(data)
      message.success(t('common.createSuccess'))
    } else {
      await AsmtModelApi.updateAsmtIndex(data)
      message.success(t('common.updateSuccess'))
    }
    dialogVisible.value = false
    // 发送操作成功的事件
    emit('success')
  } finally {
    formLoading.value = false
  }
}

/** 重置表单 */
const resetForm = () => {
  formData.value = {
    id: undefined,
    indexName: undefined,
    indexGroup: undefined,
    modelId: undefined,
    level: 1,
    indexCode: undefined,
    rule: undefined,
    description: undefined,
    isImport: props.isImport,
    status: 0,
    coverType: 0,
  }
  formRef.value?.resetFields()
}
</script>
