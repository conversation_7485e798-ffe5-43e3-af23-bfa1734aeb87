<template>
  <Dialog :title="dialogTitle" v-model="dialogVisible">
    <el-form
      ref="formRef"
      :model="formData"
      :rules="formRules"
      label-width="100px"
      v-loading="formLoading"
    >
      <el-form-item label="指标名称" prop="indexName">
        <el-input v-model="formData.indexName" placeholder="请输入指标名称" />
      </el-form-item>
      <el-form-item label="组名" prop="indexGroup">
        <el-input v-model="formData.indexGroup" placeholder="请输入组名" />
      </el-form-item>
      <el-form-item label="模型id" prop="modelId">
        <el-input v-model="formData.modelId" placeholder="请输入模型id" />
      </el-form-item>
      <el-form-item label="指标级别" prop="level">
        <el-input v-model="formData.level" placeholder="请输入指标级别" />
      </el-form-item>
      <el-form-item label="计算规则表达式" prop="rule">
        <el-input v-model="formData.rule" placeholder="请输入计算规则表达式" />
      </el-form-item>
      <el-form-item label="说明" prop="description">
        <el-input v-model="formData.description" type="textarea" placeholder="请输入说明" />
      </el-form-item>
      <el-form-item label="是否导入指标" prop="isImport">
        <el-radio-group v-model="formData.isImport">
          <el-radio value="1">请选择字典生成</el-radio>
        </el-radio-group>
      </el-form-item>
      <el-form-item label="状态" prop="status">
          <el-radio-group v-model="formData.status">
            <el-radio
              v-for="dict in getIntDictOptions(DICT_TYPE.COMMON_STATUS)"
              :key="dict.value"
              :label="dict.value"
            >
              {{ dict.label }}
            </el-radio>
          </el-radio-group>
        </el-form-item>
    </el-form>
    <template #footer>
      <el-button @click="submitForm" type="primary" :disabled="formLoading">确 定</el-button>
      <el-button @click="dialogVisible = false">取 消</el-button>
    </template>
  </Dialog>
</template>
<script setup lang="ts">
import { AsmtIndexApi, AsmtIndexVO } from '@/api/hrm/asmtindex'
import {getIntDictOptions, getStrDictOptions, DICT_TYPE} from '@/utils/dict'

/** 测评指标 表单 */
defineOptions({ name: 'AsmtIndexForm' })

const { t } = useI18n() // 国际化
const message = useMessage() // 消息弹窗

const dialogVisible = ref(false) // 弹窗的是否展示
const dialogTitle = ref('') // 弹窗的标题
const formLoading = ref(false) // 表单的加载中：1）修改时的数据加载；2）提交的按钮禁用
const formType = ref('') // 表单的类型：create - 新增；update - 修改
const formData = ref({
  id: undefined,
  indexName: undefined,
  indexGroup: undefined,
  modelId: undefined,
  level: undefined,
  rule: undefined,
  description: undefined,
  isImport: undefined,
  status: undefined
})
const formRules = reactive({
  indexName: [{ required: true, message: '指标名称不能为空', trigger: 'blur' }]
})
const formRef = ref() // 表单 Ref

/** 打开弹窗 */
const open = async (type: string, id?: number) => {
  dialogVisible.value = true
  dialogTitle.value = t('action.' + type)
  formType.value = type
  resetForm()
  // 修改时，设置数据
  if (id) {
    formLoading.value = true
    try {
      formData.value = await AsmtIndexApi.getAsmtIndex(id)
    } finally {
      formLoading.value = false
    }
  }
}
defineExpose({ open }) // 提供 open 方法，用于打开弹窗

/** 提交表单 */
const emit = defineEmits(['success']) // 定义 success 事件，用于操作成功后的回调
const submitForm = async () => {
  // 校验表单
  await formRef.value.validate()
  // 提交请求
  formLoading.value = true
  try {
    const data = formData.value as unknown as AsmtIndexVO
    if (formType.value === 'create') {
      await AsmtIndexApi.createAsmtIndex(data)
      message.success(t('common.createSuccess'))
    } else {
      await AsmtIndexApi.updateAsmtIndex(data)
      message.success(t('common.updateSuccess'))
    }
    dialogVisible.value = false
    // 发送操作成功的事件
    emit('success')
  } finally {
    formLoading.value = false
  }
}

/** 重置表单 */
const resetForm = () => {
  formData.value = {
    id: undefined,
    indexName: undefined,
    indexGroup: undefined,
    modelId: undefined,
    level: undefined,
    rule: undefined,
    description: undefined,
    isImport: undefined,
    status: undefined
  }
  formRef.value?.resetFields()
}
</script>