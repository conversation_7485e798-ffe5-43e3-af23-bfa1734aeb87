<template>
  <ContentWrap>
    <!-- 搜索工作栏 -->
    <el-form
      class="-mb-15px"
      :model="queryParams"
      ref="queryFormRef"
      :inline="true"
      label-width="68px"
    >
      <el-form-item label="任务名称" prop="taskName">
        <el-input
          v-model="queryParams.taskName"
          placeholder="请输入任务名称"
          clearable
          @keyup.enter="handleQuery"
          class="!w-240px"
        />
      </el-form-item>
      <el-form-item label="试卷名称" prop="paperName">
        <el-input
          v-model="queryParams.paperName"
          placeholder="请输入试卷名称"
          clearable
          @keyup.enter="handleQuery"
          class="!w-240px"
        />
      </el-form-item>
      <el-form-item label="用户姓名" prop="nickname">
        <el-input
          v-model="queryParams.nickname"
          placeholder="请输入用户姓名"
          clearable
          @keyup.enter="handleQuery"
          class="!w-240px"
        />
      </el-form-item>
      <el-form-item>
        <el-button @click="handleQuery"><Icon icon="ep:search" class="mr-5px" /> 搜索</el-button>
        <el-button @click="resetQuery"><Icon icon="ep:refresh" class="mr-5px" /> 重置</el-button>
        <el-button
          type="success"
          plain
          @click="handleExport"
          :loading="exportLoading"
          v-hasPermi="['hrm:asmt-answersheet:export']"
        >
          <Icon icon="ep:download" class="mr-5px" /> 导出
        </el-button>
      </el-form-item>
    </el-form>
  </ContentWrap>

  <!-- 列表 -->
  <ContentWrap>
    <el-table v-loading="loading" :data="list" :stripe="true" :show-overflow-tooltip="true">
<!--      <el-table-column label="流水号" prop="id" width="120" />-->
      <el-table-column label="姓名" prop="nickname" width="120"/>
      <el-table-column label="测评任务" prop="taskName" />
      <el-table-column label="测评卷" prop="paperName" />
      <el-table-column label="测评类型" prop="asmtType" width="150">
        <template #default="scope">
          <dict-tag :type="DICT_TYPE.HRM_ASMT_TYPE" :value="scope.row.asmtType" />
        </template>
      </el-table-column>
      <el-table-column
        label="评测时间"
        prop="createTime"
        :formatter="dateFormatter"
        width="180px"
      />
      <el-table-column label="操作" align="center" width="160px">
        <template #default="scope">
          <el-button
            link
            type="primary"
            @click="showDetail(scope.row)"
          >
            查看明细
          </el-button>
          <el-button
            link
            type="danger"
            @click.stop="handleDelete(scope.row)"
            v-hasPermi="['hrm:asmt-answersheet:delete']"
          >
            删除
          </el-button>
        </template>
      </el-table-column>
    </el-table>
    <!-- 分页 -->
    <Pagination
      :total="total"
      v-model:page="queryParams.pageNo"
      v-model:limit="queryParams.pageSize"
      @pagination="getList"
    />
  </ContentWrap>

  <!-- 表单弹窗：添加/修改 -->
  <AsmtAnswersheetForm ref="formRef" @success="getList" />
  <el-drawer title="答题明细" v-model="showSheetDetail" size="70%">
    <template #title>
      <div style="font-size: 20px; color: black;" v-if="currentPaper">
        任务：{{currentPaper.taskName}}
        <div style="font-size: 70%; color: #666">
          答题人：{{currentPaper.nickname}}，
          答题时间：{{formatDate(currentPaper.createTime)}}
        </div>
      </div>
    </template>
    <answer-sheet-list v-if="showSheetDetail"  :user-task-id="currentPaper.userTaskId" :paper-id="currentPaper.paperId"/>
  </el-drawer>
</template>

<script setup lang="ts">
import download from '@/utils/download'
import {dateFormatter, formatDate} from '@/utils/formatTime'
import { AsmtAnswersheetApi, AsmtAnswersheetVO } from '@/api/hrm/answersheet'
import AsmtAnswersheetForm from './AsmtAnswersheetForm.vue'
import {DICT_TYPE} from "@/utils/dict";
import AnswerSheetList from "@/views/hrm/answersheet/AnswerSheetList.vue";

/** 用户答题卡 列表 */
defineOptions({ name: 'AsmtAnswersheet' })

const message = useMessage() // 消息弹窗

const loading = ref(true) // 列表的加载中
const list = ref<AsmtAnswersheetVO[]>([]) // 列表的数据
const total = ref(0) // 列表的总页数
const queryParams = reactive({
  pageNo: 1,
  pageSize: 10,
  userId: undefined,
  taskId: undefined,
  pkgId: undefined,
  paperId: undefined,
  quesId: undefined,
  nickname: undefined,
  taskName: undefined,
  paperName: undefined,
})
const queryFormRef = ref() // 搜索的表单
const exportLoading = ref(false) // 导出的加载中
const { query} = useRoute() // taskId 任务ID
// taskId

/** 查询列表 */
const getList = async () => {
  loading.value = true
  try {
    const data = await AsmtAnswersheetApi.getAsmtAnswersheetPage(queryParams)
    list.value = data.list
    total.value = data.total
  } finally {
    loading.value = false
  }
}

/** 查看答题明细 */
const showSheetDetail = ref(false)
const currentPaper = ref(null)
const showDetail = async (row) => {
  currentPaper.value = row;
  showSheetDetail.value = true;
}
//
/** 搜索按钮操作 */
const handleQuery = () => {
  queryParams.pageNo = 1
  getList()
}

/** 删除按钮操作 */
const handleDelete = async (row: any) => {
  try {
    // 删除的二次确认
    await message.delConfirm()
    // 发起删除
    await AsmtAnswersheetApi.deleteByUserTaskIdAndPaperId(row.userTaskId, row.paperId)
    message.success('删除成功')
    // 刷新列表
    await getList()
  } catch {
  }
}

/** 重置按钮操作 */
const resetQuery = () => {
  queryFormRef.value.resetFields()
  handleQuery()
}

/** 导出按钮操作 */
const handleExport = async () => {
  try {
    // 导出的二次确认
    await message.exportConfirm()
    // 发起导出
    exportLoading.value = true
    const data = await AsmtAnswersheetApi.exportAsmtAnswersheet(queryParams)
    download.excel(data, '用户答题卡.xls')
  } catch {
  } finally {
    exportLoading.value = false
  }
}

/** 初始化 **/
onMounted(() => {
  if (query.taskId) {
    queryParams.taskId = query.taskId
  }
  if (query.userId) {
    queryParams.userId = query.userId
  }
  getList()
})
</script>
