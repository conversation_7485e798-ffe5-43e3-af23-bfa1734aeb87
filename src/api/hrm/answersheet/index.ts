import request from '@/config/axios'

// 用户答题卡 VO
export interface AsmtAnswersheetVO {
  id: number // 标签id
  userId: number // 用户id
  taskId: number // 测评任务id
  pkgId: number // 测试包id
  paperId: number // 测试卷id
  quesId: number // 题目id
  answer: string // 答案
  score: number // 得分
}

// 用户答题卡 API
export const AsmtAnswersheetApi = {
  // 查询用户答题卡分页
  getAsmtAnswersheetPage: async (params: any) => {
    return await request.get({ url: `/hrm/asmt-answersheet/page`, params })
  },

  // 查询用户答题卡详情
  getAsmtAnswersheet: async (id: number) => {
    return await request.get({ url: `/hrm/asmt-answersheet/get?id=` + id })
  },

  // 查询用户答题卡详情
  getAsmtAnswersheetList: async (id: number, paperId: number) => {
    return await request.get({ url: `/hrm/asmt-answersheet/get-list?userTaskId=${id}&paperId=${paperId}` })
  },

  // 新增用户答题卡
  createAsmtAnswersheet: async (data: AsmtAnswersheetVO[]) => {
    return await request.post({ url: `/hrm/asmt-answersheet/create`, data })
  },

  // 修改用户答题卡
  updateAsmtAnswersheet: async (data: AsmtAnswersheetVO) => {
    return await request.put({ url: `/hrm/asmt-answersheet/update`, data })
  },

  // 删除用户答题卡
  deleteByUserTaskIdAndPaperId: async (userTaskId: number, paperId: number) => {
    return await request.delete({ url: `/hrm/asmt-answersheet/delete/by-task?userTaskId=${userTaskId}&paperId=${paperId}`})
  },

  // 导出用户答题卡 Excel
  exportAsmtAnswersheet: async (id: number, paperId: number) => {
    return await request.download({ url: `/hrm/asmt-answersheet/export-excel?userTaskId=${id}&paperId=${paperId}` })
  }
}
