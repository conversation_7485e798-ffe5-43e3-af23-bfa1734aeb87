import request from '@/config/axios'

// 测评任务-任务用户关联 VO
export interface AsmtTaskUserVO {
  id: number // 标签id
  taskId: number // 测评任务id
  userId: number // 用户id
}

export interface AsmtInviteUserVo {
  nickname: string,
  remark: string,
  email: string,
  mobile: string,
  taskId: number,
}

// 测评任务-任务用户关联 API
export const AsmtTaskUserApi = {
  // 查询测评任务-任务用户关联分页
  getAsmtTaskUserPage: async (params: any) => {
    return await request.get({ url: `/hrm/asmt-task-user/page`, params })
  },
  search: async (params: any) => {
    return await request.get({ url: `/hrm/asmt-task-user/search`, params })
  },

  listByTask: async (taskId: number, isAnswer: boolean) => {
    return await request.get({ url: `/hrm/asmt-task-user/list-by-task?taskId=${taskId}&isAnswer=${isAnswer}`})
  },

  getAnsweringList: async () => {
    return await request.get({ url: `/hrm/asmt-task-user/answering-list`})
  },

  deleteAnswerMark: async (userTaskId: number, paperId: number) => {
    return await request.delete({ url: `/hrm/asmt-answersheet/answer-mark/delete?userTaskId=${userTaskId}&paperId=${paperId}`})
  },

  // 查询测评任务-任务用户关联详情
  getAsmtTaskUser: async (id: number) => {
    return await request.get({ url: `/hrm/asmt-task-user/get?id=` + id })
  },

  /**
   * 获取测评任务详情
   * @param userTaskId 任务-用户关联表的ID
   */
  getUserTaskDetail: async (userTaskId: any) => {
    return request.get({ url: `/hrm/asmt-task-user/get-user-task-detail?userTaskId=` + userTaskId })
  },
  getMyTasks: async (data?: any) => {
    return request.post({ url: `/hrm/asmt-task-user/my-task`, data})
  },

  // 新增测评任务-任务用户关联
  createAsmtTaskUser: async (data: AsmtTaskUserVO) => {
    return await request.post({ url: `/hrm/asmt-task-user/create`, data })
  },

  // 邀请外部新用户
  inviteTaskUser: async (data: AsmtInviteUserVo) => {
    return await request.post({ url: `/hrm/asmt-task-user/invite-outside`, data })
  },

  // 批量新增测评任务-任务用户关联
  createAsmtTaskUserBatch: async (data: AsmtTaskUserVO[]) => {
    return await request.post({ url: `/hrm/asmt-task-user/create-batch`, data })
  },

  // 修改测评任务-任务用户关联
  updateAsmtTaskUser: async (data: AsmtTaskUserVO) => {
    return await request.put({ url: `/hrm/asmt-task-user/update`, data })
  },

  // 删除测评任务-任务用户关联
  deleteAsmtTaskUser: async (id: number) => {
    return await request.delete({ url: `/hrm/asmt-task-user/delete?id=` + id })
  },

  // 导出测评任务-任务用户关联 Excel
  exportAsmtTaskUser: async (params) => {
    return await request.download({ url: `/hrm/asmt-task-user/export-excel`, params })
  }
}
