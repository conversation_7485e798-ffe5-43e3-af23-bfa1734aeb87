import request from '@/config/axios'

// 测评指标计算结果 VO
export interface AsmtIndexResultVO {
  id: number // id
  userId: number // 用户id
  taskId: number // 任务id
  modelId: number // 模型id
  indexId: number // 指标id
  indexGroup: string // 分组
  indexCode: string // 编码
  score: number // 得分
  stdScore: number // 标准分
  refValue: string // 参考值
  isImport: boolean // 是否为导入数据
  description: string // 结果描述
}

// 测评指标计算结果 API
export const AsmtIndexResultApi = {
  // 查询测评指标计算结果分页
  calcIndexResult: async (taskId: number, userId: number) => {
    return await request.post({ url: `/hrm/asmt-index-result/calc-index-result?userId=${userId}&taskId=${taskId}` })
  },
  // 查询测评指标计算结果分页
  getAsmtIndexResultPage: async (params: any) => {
    return await request.get({ url: `/hrm/asmt-index-result/page`, params })
  },

  getTaskIndexResultData: async (taskId: any, userId: any) => {
    return await request.get({url: `/hrm/asmt-index-result/map-by-user?taskId=${taskId}&userId=${userId}`})
  },

  // 查询测评指标计算结果详情
  getAsmtIndexResult: async (id: number) => {
    return await request.get({ url: `/hrm/asmt-index-result/get?id=` + id })
  },

  // 新增测评指标计算结果
  createAsmtIndexResult: async (data: AsmtIndexResultVO) => {
    return await request.post({ url: `/hrm/asmt-index-result/create`, data })
  },

  // 修改测评指标计算结果
  updateAsmtIndexResult: async (data: AsmtIndexResultVO) => {
    return await request.put({ url: `/hrm/asmt-index-result/update`, data })
  },

  // 删除测评指标计算结果
  deleteAsmtIndexResult: async (id: number) => {
    return await request.delete({ url: `/hrm/asmt-index-result/delete?id=` + id })
  },

  // 批量删除测评指标计算结果
  deleteAsmtIndexResultBatch: async (data: any) => {
    return await request.post({ url: `/hrm/asmt-index-result/delete-by-ids`, data})
  },

  // 导出测评指标计算结果 Excel
  exportAsmtIndexResult: async (params) => {
    return await request.download({ url: `/hrm/asmt-index-result/export`, params })
  }
}
