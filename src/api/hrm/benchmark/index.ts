import request from '@/config/axios'

// 序列岗位指标基准 VO
export interface AsmtIndexBenchmarkVO {
  id: number // id
  series: string // 系列
  level: string // 层级
  hx1: number // 固有聪慧
  hx2: number // 科学认知
  hx3: number // 反省利他
  hx4: number // AI思维
  hx5: number // 乐群凝聚
  hx6: number // 魄力果敢
  hx7: number // 系统思维
  hx8: number // 专业特质
  hx9: number // 深度思考
  hx10: number // 简单执着
  hx11: number // 创新突破
  hx12: number // 业绩证明
  levelAverage: number // 层次均值
  hx1Weight: number,
  hx2Weight: number,
  hx3Weight: number,
  hx4Weight: number,
  hx5Weight: number,
  hx6Weight: number,
  hx7Weight: number,
  hx8Weight: number,
  hx9Weight: number,
  hx10Weight: number,
  hx11Weight: number,
  hx12Weight: number,
}

// 序列岗位指标基准 API
export const AsmtIndexBenchmarkApi = {
  // 查询序列岗位指标基准分页
  getAsmtIndexBenchmarkPage: async (params: any) => {
    return await request.get({ url: `/hrm/asmt-index-benchmark/page`, params })
  },

  // 查询序列岗位指标基准详情
  getAsmtIndexBenchmark: async (id: number) => {
    return await request.get({ url: `/hrm/asmt-index-benchmark/get?id=` + id })
  },

  // 查询序列岗位指标基准详情
  getIndexBenchmarkSeriesLevel: async (series: string, level: string) => {
    return await request.get({ url: `/hrm/asmt-index-benchmark/get-by-series-level?series=${series}&level=${level}`})
  },
  getIndexBenchmarkSeriesLevelByUser: async (userId: number) => {
    return await request.get({ url: `/hrm/asmt-index-benchmark/get-by-user?userId=${userId}`})
  },

  // 新增序列岗位指标基准
  createAsmtIndexBenchmark: async (data: AsmtIndexBenchmarkVO) => {
    return await request.post({ url: `/hrm/asmt-index-benchmark/create`, data })
  },

  // 修改序列岗位指标基准
  updateAsmtIndexBenchmark: async (data: AsmtIndexBenchmarkVO) => {
    return await request.put({ url: `/hrm/asmt-index-benchmark/update`, data })
  },

  // 删除序列岗位指标基准
  deleteAsmtIndexBenchmark: async (id: number) => {
    return await request.delete({ url: `/hrm/asmt-index-benchmark/delete?id=` + id })
  },

  // 导出序列岗位指标基准 Excel
  exportAsmtIndexBenchmark: async (params) => {
    return await request.download({ url: `/hrm/asmt-index-benchmark/export-excel`, params })
  }
}
